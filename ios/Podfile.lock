PODS:
  - agora_rtc_engine (6.5.0):
    - AgoraIrisRTC_iOS (= 4.5.0-build.1)
    - AgoraRtcEngine_iOS (= 4.5.0)
    - Flutter
  - AgoraInfra_iOS (1.2.13)
  - AgoraIrisRTC_iOS (4.5.0-build.1)
  - AgoraRtcEngine_iOS (4.5.0):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.0)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.0)
    - AgoraRtcEngine_iOS/AINS (= 4.5.0)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.0)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.0)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.0)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.0)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.0)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.0)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.0)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.0)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.0)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.0)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.0)
    - AgoraRtcEngine_iOS/VQA (= 4.5.0)
  - AgoraRtcEngine_iOS/AIAEC (4.5.0)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.0)
  - AgoraRtcEngine_iOS/AINS (4.5.0)
  - AgoraRtcEngine_iOS/AINSLL (4.5.0)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.0)
  - AgoraRtcEngine_iOS/ClearVision (4.5.0)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.0)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.0)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.0)
  - AgoraRtcEngine_iOS/LipSync (4.5.0)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.0)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.0):
    - AgoraInfra_iOS (= 1.2.13)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.0)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.0)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.0)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.0)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.0)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.0)
  - AgoraRtcEngine_iOS/VQA (4.5.0)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 17.0)
    - Flutter
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Auth (10.25.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.25.0)
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - firebase_auth (4.16.0):
    - Firebase/Auth (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.25.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - Flutter (1.0.0)
  - flutter_inapp_purchase (0.0.1):
    - Flutter
  - flutter_sound (9.6.0):
    - Flutter
    - flutter_sound_core (= 9.6.0)
  - flutter_sound_core (9.6.0)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - iris_method_channel (0.0.1):
    - Flutter
  - isar_flutter_libs (1.0.0):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (100.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - rongcloud_im_wrapper_plugin (0.0.1):
    - Flutter
    - RongCloudIM/ChatRoom (= 5.12.1)
    - RongCloudIM/IMLibCore (= 5.12.1)
  - RongCloudIM/ChatRoom (5.12.1):
    - RongCloudIM/IMLibCore
  - RongCloudIM/IMLibCore (5.12.1)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_inapp_purchase (from `.symlinks/plugins/flutter_inapp_purchase/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - rongcloud_im_wrapper_plugin (from `.symlinks/plugins/rongcloud_im_wrapper_plugin/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - AppAuth
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - flutter_sound_core
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - RecaptchaInterop
    - RongCloudIM
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_inapp_purchase:
    :path: ".symlinks/plugins/flutter_inapp_purchase/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  rongcloud_im_wrapper_plugin:
    :path: ".symlinks/plugins/rongcloud_im_wrapper_plugin/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  agora_rtc_engine: bc056a728ed1be8261074a4baf3487e99a6c9a9a
  AgoraInfra_iOS: 65e11a2183ab7836258768868d06058c22701b13
  AgoraIrisRTC_iOS: 8207edf56ceaae6b8ee773d8e2137c6b7cb062be
  AgoraRtcEngine_iOS: 38b5e0a49bbad5616f284e6a4f9e4f5bb3fb3b2e
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  facebook_app_events: 049595ecd654a675820f3150f466fa0af1e91478
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_auth: 9cfa74cc3773c44d2f762c3baa0806234c8516f5
  firebase_core: 3b49a055ff54114cae400581c13671fe53936c36
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: c0f93dcc570c9da2bffb576969d793e95c344fbb
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inapp_purchase: 722da12971a50f306f37e62fc1aaf576b1cbecf6
  flutter_sound: d266936e09c39c3c3cc2dfc06758b954ff6ff7b5
  flutter_sound_core: 0c6eb9d5268adc70ff159b3d65fd3d98a82d3a27
  google_sign_in_ios: 0ab078e60da6dfe23cbc55c83502b52bba1aad63
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  iris_method_channel: b9db2053dac3dc84e256c8792eff6f11323a53bd
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  just_audio: 6c031bb61297cf218b4462be616638e81c058e97
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  rongcloud_im_wrapper_plugin: 0b86e71b909e58aa3cb52e7f1132506c769f900b
  RongCloudIM: 6999997ece7d7b9a0bdd879b11bbd9b841b75385
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: 20e260c9bb3f61194c661a4ba028da86e4f3ed96

COCOAPODS: 1.16.2
