arguments=--init-script C\:\\Users\\Administrator\\AppData\\Roaming\\TabNine\\language-servers\\bundled_java\\1.24.0\\app\\configuration\\org.eclipse.osgi\\53\\0\\.cp\\gradle\\init\\init.gradle
auto.sync=false
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=D\:/.gradle
java.home=D\:/Program Files/Android/Android Studio/jbr
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
