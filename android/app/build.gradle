plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader -> localProperties.load(reader)
    }
}

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

android {
    namespace = "com.mobile.app.chamatch"
    compileSdk = flutter.compileSdkVersion

    ndkVersion = "27.0.12077973"
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        applicationId = "com.mobile.app.chamatchpro"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "arm64-v8a","armeabi-v7a","x86","x86_64"
        }
    }

    buildFeatures.buildConfig true

    signingConfigs {
        config {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            buildConfigField "boolean", "LOG_DEBUG", "false"
            minifyEnabled false
            shrinkResources false
            zipAlignEnabled true
            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            signingConfig signingConfigs.config
        }

        applicationVariants.all { variant ->
            variant.outputs.all { output -> outputFileName = "app_v${flutterVersionName}_${flutterVersionCode}_${variant.buildType.name}.apk"
            }
        }
    }

    lintOptions {
        checkDependencies true
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
        // The demo app does not have translations.
        disable 'MissingTranslation'
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.annotation:annotation-jvm:1.9.1'
    implementation 'androidx.core:core:1.13.1'
    implementation 'cn.rongcloud.sdk:im_libcore:5.12.1'
    implementation 'cn.rongcloud.sdk:im_chatroom:5.12.1'
    implementation 'commons-codec:commons-codec:1.6'
    implementation 'androidx.preference:preference:1.2.1'
    implementation 'com.android.billingclient:billing:7.1.1'
    implementation 'com.android.installreferrer:installreferrer:2.2'
}
tasks.whenTaskAdded { task ->
    if (task.name == "generateDebugUnitTestConfig") {
        println("generateDebugUnitTestConfig task: ${task.name}")
        task.enabled = false
    }
    if (task.name.contains("testDebugUnitTest")) {
        println("Disabling task: ${task.name}")
        task.enabled = false
    }
}