package com.mobile.app.chamatch

import android.os.Parcel
import android.os.Parcelable
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import org.json.JSONObject
import java.nio.charset.StandardCharsets

/** 戳一下消息 */
@MessageTag(value = "mikchat:gift", flag = MessageTag.ISCOUNTED)
class MikChatGiftMessage() : MessageContent(), Parcelable {

    var type: String? = null
    var channelId: String? = null
    var giftCode: String? = null
    var userId: String? = null
    var userCategory: String? = null
    var headFileName: String? = null
    var giveNum: String? = null
    var nickName: String? = null
    var userRole: String? = null
    var giftUrl: String? = null

    constructor(data: ByteArray) : this() {
        try {
            val jsonStr = String(data, StandardCharsets.UTF_8)
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has("type")) {
                type = jsonObj.optString("type")
            }
            if (jsonObj.has("channelId")) {
                channelId = jsonObj.optString("channelId")
            }
            if (jsonObj.has("giftCode")) {
                giftCode = jsonObj.optString("giftCode")
            }
            if (jsonObj.has("userId")) {
                userId = jsonObj.optString("userId")
            }
            if (jsonObj.has("userCategory")) {
                userCategory = jsonObj.optString("userCategory")
            }
            if (jsonObj.has("headFileName")) {
                headFileName = jsonObj.optString("headFileName")
            }
            if (jsonObj.has("giveNum")) {
                giveNum = jsonObj.optString("giveNum")
            }
            if (jsonObj.has("nickName")) {
                nickName = jsonObj.optString("nickName")
            }
            if (jsonObj.has("userRole")) {
                userRole = jsonObj.optString("userRole")
            }
            if (jsonObj.has("giftUrl")) {
                giftUrl = jsonObj.optString("giftUrl")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    constructor(parcel: Parcel) : this() {
        type = ParcelUtils.readFromParcel(parcel)
        channelId = ParcelUtils.readFromParcel(parcel)
        giftCode = ParcelUtils.readFromParcel(parcel)
        userId = ParcelUtils.readFromParcel(parcel)
        userCategory = ParcelUtils.readFromParcel(parcel)
        headFileName = ParcelUtils.readFromParcel(parcel)
        giveNum = ParcelUtils.readFromParcel(parcel)
        nickName = ParcelUtils.readFromParcel(parcel)
        userRole = ParcelUtils.readFromParcel(parcel)
        giftUrl = ParcelUtils.readFromParcel(parcel)
    }

    override fun encode(): ByteArray? {
        return try {
            val jsonObj = JSONObject()
            jsonObj.put("type", type)
            jsonObj.put("giftCode", giftCode)
            jsonObj.put("userId", userId)
            jsonObj.put("userCategory", userCategory)
            jsonObj.put("headFileName", headFileName)
            jsonObj.put("giveNum", giveNum)
            jsonObj.put("nickName", nickName)
            jsonObj.put("userRole", userRole)
            jsonObj.put("giftUrl", giftUrl)
            jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, type)
        ParcelUtils.writeToParcel(dest, giftCode)
        ParcelUtils.writeToParcel(dest, userId)
        ParcelUtils.writeToParcel(dest, userCategory)
        ParcelUtils.writeToParcel(dest, headFileName)
        ParcelUtils.writeToParcel(dest, giveNum)
        ParcelUtils.writeToParcel(dest, nickName)
        ParcelUtils.writeToParcel(dest, userRole)
        ParcelUtils.writeToParcel(dest, giftUrl)
    }

    companion object {
        @JvmStatic
        fun obtain(
            type: String?, channelId: String?, userId: String?, userCategory: String?,
            giftCode: String?, headFileName: String?, giveNum: String?, nickName: String?,
            userRole: String?, giftUrl: String?
        ): MikChatGiftMessage {
            return MikChatGiftMessage().apply {
                this.type = type
                this.channelId = channelId
                this.userId = userId
                this.userCategory = userCategory
                this.giftCode = giftCode
                this.headFileName = headFileName
                this.giveNum = giveNum
                this.nickName = nickName
                this.userRole = userRole
                this.giftUrl = giftUrl
            }
        }

        @JvmField
        val CREATOR: Parcelable.Creator<MikChatGiftMessage> =
            object : Parcelable.Creator<MikChatGiftMessage> {
                override fun createFromParcel(source: Parcel): MikChatGiftMessage {
                    return MikChatGiftMessage(source)
                }

                override fun newArray(size: Int): Array<MikChatGiftMessage?> {
                    return arrayOfNulls(size)
                }
            }
    }
}
