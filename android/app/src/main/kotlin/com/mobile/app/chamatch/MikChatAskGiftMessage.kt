package com.mobile.app.chamatch

import android.os.Parcel
import android.os.Parcelable
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import org.json.JSONObject
import java.nio.charset.StandardCharsets

/** 戳一下消息 */
@MessageTag(value = "mikchat:askgift", flag = MessageTag.ISCOUNTED)
class MikChatAskGiftMessage() : MessageContent(), Parcelable {

    /** 戳一下内容 */
    var type: String? = null
    var channelId: String? = null
    var giftCode: String? = null
    var userId: String? = null
    var userCategory: String? = null
    var headFileName: String? = null
    var giveNum: String? = null
    var nickName: String? = null
    var userRole: String? = null
    var giftUrl: String? = null
    var giftName: String? = null
    var giftPrice: String? = null
    var giftId: String? = null

    constructor(data: ByteArray) : this() {
        try {
            val jsonStr = String(data, StandardCharsets.UTF_8)
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has("type")) {
                type = jsonObj.optString("type")
            }
            if (jsonObj.has("channelId")) {
                channelId = jsonObj.optString("channelId")
            }
            if (jsonObj.has("giftCode")) {
                giftCode = jsonObj.optString("giftCode")
            }
            if (jsonObj.has("userId")) {
                userId = jsonObj.optString("userId")
            }
            if (jsonObj.has("userCategory")) {
                userCategory = jsonObj.optString("userCategory")
            }
            if (jsonObj.has("headFileName")) {
                headFileName = jsonObj.optString("headFileName")
            }
            if (jsonObj.has("giveNum")) {
                giveNum = jsonObj.optString("giveNum")
            }
            if (jsonObj.has("nickName")) {
                nickName = jsonObj.optString("nickName")
            }
            if (jsonObj.has("userRole")) {
                userRole = jsonObj.optString("userRole")
            }
            if (jsonObj.has("giftUrl")) {
                giftUrl = jsonObj.optString("giftUrl")
            }
            if (jsonObj.has("giftName")) {
                giftName = jsonObj.optString("giftName")
            }
            if (jsonObj.has("giftPrice")) {
                giftPrice = jsonObj.optString("giftPrice")
            }
            if (jsonObj.has("giftId")) {
                giftId = jsonObj.optString("giftId")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    constructor(parcel: Parcel) : this() {
        type = ParcelUtils.readFromParcel(parcel)
        channelId = ParcelUtils.readFromParcel(parcel)
        giftCode = ParcelUtils.readFromParcel(parcel)
        userId = ParcelUtils.readFromParcel(parcel)
        userCategory = ParcelUtils.readFromParcel(parcel)
        headFileName = ParcelUtils.readFromParcel(parcel)
        giveNum = ParcelUtils.readFromParcel(parcel)
        nickName = ParcelUtils.readFromParcel(parcel)
        userRole = ParcelUtils.readFromParcel(parcel)
        giftUrl = ParcelUtils.readFromParcel(parcel)
        giftName = ParcelUtils.readFromParcel(parcel)
        giftPrice = ParcelUtils.readFromParcel(parcel)
        giftId = ParcelUtils.readFromParcel(parcel)
    }

    override fun encode(): ByteArray? {
        return try {
            val jsonObj = JSONObject()
            jsonObj.put("type", type)
            jsonObj.put("channelId", channelId)
            jsonObj.put("giftCode", giftCode)
            jsonObj.put("userId", userId)
            jsonObj.put("userCategory", userCategory)
            jsonObj.put("headFileName", headFileName)
            jsonObj.put("giveNum", giveNum)
            jsonObj.put("nickName", nickName)
            jsonObj.put("userRole", userRole)
            jsonObj.put("giftUrl", giftUrl)
            jsonObj.put("giftName", giftName)
            jsonObj.put("giftPrice", giftPrice)
            jsonObj.put("giftId", giftId)
            jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, type)
        ParcelUtils.writeToParcel(dest, channelId)
        ParcelUtils.writeToParcel(dest, giftCode)
        ParcelUtils.writeToParcel(dest, userId)
        ParcelUtils.writeToParcel(dest, userCategory)
        ParcelUtils.writeToParcel(dest, headFileName)
        ParcelUtils.writeToParcel(dest, giveNum)
        ParcelUtils.writeToParcel(dest, nickName)
        ParcelUtils.writeToParcel(dest, userRole)
        ParcelUtils.writeToParcel(dest, giftUrl)
        ParcelUtils.writeToParcel(dest, giftName)
        ParcelUtils.writeToParcel(dest, giftPrice)
        ParcelUtils.writeToParcel(dest, giftId)
    }

    companion object {
        @JvmStatic
        fun obtain(
            type: String?,
            channelId: String?,
            giftCode: String?,
            userId: String?,
            userCategory: String?,
            headFileName: String?,
            giveNum: String?,
            nickName: String?,
            userRole: String?,
            giftUrl: String?,
            giftName: String?,
            giftPrice: String?,
            giftId: String?
        ): MikChatAskGiftMessage {
            return MikChatAskGiftMessage().apply {
                this.type = type
                this.channelId = channelId
                this.giftCode = giftCode
                this.userId = userId
                this.userCategory = userCategory
                this.headFileName = headFileName
                this.giveNum = giveNum
                this.nickName = nickName
                this.userRole = userRole
                this.giftUrl = giftUrl
                this.giftName = giftName
                this.giftPrice = giftPrice
                this.giftId = giftId
            }
        }

        @JvmField
        val CREATOR: Parcelable.Creator<MikChatAskGiftMessage> =
            object : Parcelable.Creator<MikChatAskGiftMessage> {
                override fun createFromParcel(source: Parcel): MikChatAskGiftMessage {
                    return MikChatAskGiftMessage(source)
                }

                override fun newArray(size: Int): Array<MikChatAskGiftMessage?> {
                    return arrayOfNulls(size)
                }
            }
    }
}
