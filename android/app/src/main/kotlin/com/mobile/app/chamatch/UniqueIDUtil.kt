package com.mobile.app.chamatch

import android.annotation.TargetApi
import android.preference.PreferenceManager

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/1/7 16:35
 * @description :
 */
import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID

object UniqueIDUtil {

    private const val TAG = "UniqueIDUtils"
    private var uniqueID: String? = null
    private const val uniqueKey = "unique_id"

    //    private val uniqueIDDirPath: String = Environment.getStorageDirectory().path
    private const val uniqueIDFile = "unique.txt"

    private fun getUniqueIDDirPath(context: Context): String {
        return context.filesDir.path
    }

    @TargetApi(Build.VERSION_CODES.GINGERBREAD)
    fun getUniqueID(context: Context): String {
        // 三步读取：内存中，存储的SP表中，外部存储文件中
        uniqueID?.let {
            Log.i(TAG, "getUniqueID: 内存中获取 $it")
            return it
        }

        val spUniqueID =
            PreferenceManager.getDefaultSharedPreferences(context).getString(uniqueKey, "")
        if (!spUniqueID.isNullOrEmpty()) {
            Log.i(TAG, "getUniqueID: SP中获取 $spUniqueID")
            uniqueID = spUniqueID
            return spUniqueID
        }

        readUniqueFile(context)
        if (!uniqueID.isNullOrEmpty()) {
            Log.i(TAG, "getUniqueID: 外部存储中获取 $uniqueID")
            return uniqueID!!
        }

        // 两步创建：硬件获取；自行生成与存储
        getDeviceID(context)
        getAndroidID(context)
        getSNID()
        createUniqueID(context)

        PreferenceManager.getDefaultSharedPreferences(context).edit().putString(uniqueKey, uniqueID)
            .apply()
        return uniqueID ?: ""
    }

    @SuppressLint("MissingPermission")
    private fun getDeviceID(context: Context) {
        if (!uniqueID.isNullOrEmpty()) return

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O_MR1) return

        val telephonyManager =
            context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
        try {
            val deviceId = telephonyManager?.deviceId
            if (!deviceId.isNullOrEmpty()) {
                uniqueID = deviceId
                Log.i(TAG, "getUniqueID: DeviceId获取成功 $uniqueID")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @TargetApi(Build.VERSION_CODES.CUPCAKE)
    fun getAndroidID(context: Context): String {
        var androidID = ""
        try {
            androidID =
                Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: ""
            uniqueID = androidID
            Log.i(TAG, "getUniqueID: AndroidID获取成功 $uniqueID")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return androidID
    }

    @TargetApi(Build.VERSION_CODES.GINGERBREAD)
    private fun getSNID() {
        if (!uniqueID.isNullOrEmpty()) return

        val snID = Build.SERIAL
        if (!snID.isNullOrEmpty()) {
            uniqueID = snID
            Log.i(TAG, "getUniqueID: SNID获取成功 $uniqueID")
        }
    }

    private fun createUniqueID(context: Context) {
        if (!uniqueID.isNullOrEmpty()) return

        uniqueID = UUID.randomUUID().toString()
        Log.e(TAG, "getUniqueID: UUID生成成功 $uniqueID")

        val filesDir = File(getUniqueIDDirPath(context) + File.separator + context.packageName)
        if (!filesDir.exists()) {
            filesDir.mkdir()
        }

        val file = File(filesDir, uniqueIDFile)
        if (!file.exists()) {
            try {
                file.createNewFile()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }

        try {
            FileOutputStream(file).use { outputStream ->
                outputStream.write(uniqueID!!.toByteArray())
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private fun readUniqueFile(context: Context) {
        val filesDir = File(getUniqueIDDirPath(context) + File.separator + context.packageName)
        val file = File(filesDir, uniqueIDFile)
        if (file.exists()) {
            try {
                FileInputStream(file).use { inputStream ->
                    val bytes = ByteArray(file.length().toInt())
                    inputStream.read(bytes)
                    uniqueID = String(bytes)
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun clearUniqueFile(context: Context) {
        val filesDir = File(getUniqueIDDirPath(context) + File.separator + context.packageName)
        deleteFile(filesDir)
    }

    private fun deleteFile(file: File) {
        if (file.isDirectory) {
            file.listFiles()?.forEach { deleteFile(it) }
        } else {
            file.delete()
        }
    }
}
