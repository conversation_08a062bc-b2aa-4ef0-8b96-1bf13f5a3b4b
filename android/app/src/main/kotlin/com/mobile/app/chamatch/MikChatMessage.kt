package com.mobile.app.chamatch

import android.os.Parcel
import android.os.Parcelable
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import org.json.JSONObject
import java.nio.charset.StandardCharsets

/** 戳一下消息 */
@MessageTag(value = "mikchat", flag = MessageTag.NONE)
class MikChatMessage() : MessageContent(), Parcelable {
    /** 戳一下内容 */
    var type: String? = null
    var channelId: String? = null
    var giftCode: String? = null
    var userId: String? = null
    var userCategory: String? = null
    var headFileName: String? = null
    var giveNum: String? = null
    var nickName: String? = null
    var followFlag: String? = null
    var userRole: String? = null
    var tendsId: String? = null
    var newUserFlag: String? = null
    var warnMsg: String? = null
    var content: String? = null

    constructor(data: ByteArray) : this() {
        try {
            val jsonStr = String(data, StandardCharsets.UTF_8)
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has("type")) {
                type = jsonObj.optString("type")
            }
            if (jsonObj.has("channelId")) {
                channelId = jsonObj.optString("channelId")
            }
            if (jsonObj.has("giftCode")) {
                giftCode = jsonObj.optString("giftCode")
            }
            if (jsonObj.has("userId")) {
                userId = jsonObj.optString("userId")
            }
            if (jsonObj.has("userCategory")) {
                userCategory = jsonObj.optString("userCategory")
            }
            if (jsonObj.has("headFileName")) {
                headFileName = jsonObj.optString("headFileName")
            }
            if (jsonObj.has("giveNum")) {
                giveNum = jsonObj.optString("giveNum")
            }
            if (jsonObj.has("nickName")) {
                nickName = jsonObj.optString("nickName")
            }
            if (jsonObj.has("followFlag")) {
                followFlag = jsonObj.optString("followFlag")
            }
            if (jsonObj.has("userRole")) {
                userRole = jsonObj.optString("userRole")
            }
            if (jsonObj.has("tendsId")) {
                tendsId = jsonObj.optString("tendsId")
            }
            if (jsonObj.has("newUserFlag")) {
                newUserFlag = jsonObj.optString("newUserFlag")
            }
            if (jsonObj.has("warnMsg")) {
                warnMsg = jsonObj.optString("warnMsg")
            }
            if (jsonObj.has("content")) {
                content = jsonObj.optString("content")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    constructor(parcel: Parcel) : this() {
        type = ParcelUtils.readFromParcel(parcel)
        channelId = ParcelUtils.readFromParcel(parcel)
        giftCode = ParcelUtils.readFromParcel(parcel)
        userId = ParcelUtils.readFromParcel(parcel)
        userCategory = ParcelUtils.readFromParcel(parcel)
        headFileName = ParcelUtils.readFromParcel(parcel)
        giveNum = ParcelUtils.readFromParcel(parcel)
        nickName = ParcelUtils.readFromParcel(parcel)
        followFlag = ParcelUtils.readFromParcel(parcel)
        userRole = ParcelUtils.readFromParcel(parcel)
        tendsId = ParcelUtils.readFromParcel(parcel)
        newUserFlag = ParcelUtils.readFromParcel(parcel)
        warnMsg = ParcelUtils.readFromParcel(parcel)
        content = ParcelUtils.readFromParcel(parcel)
    }

    override fun encode(): ByteArray? {
        return try {
            val jsonObj = JSONObject()
            jsonObj.put("type", type)
            jsonObj.put("channelId", channelId)
            jsonObj.put("giftCode", giftCode)
            jsonObj.put("userId", userId)
            jsonObj.put("userCategory", userCategory)
            jsonObj.put("headFileName", headFileName)
            jsonObj.put("giveNum", giveNum)
            jsonObj.put("nickName", nickName)
            jsonObj.put("followFlag", followFlag)
            jsonObj.put("userRole", userRole)
            jsonObj.put("tendsId", tendsId)
            jsonObj.put("newUserFlag", newUserFlag)
            jsonObj.put("warnMsg", warnMsg)
            jsonObj.put("content", content)
            jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, type)
        ParcelUtils.writeToParcel(dest, channelId)
        ParcelUtils.writeToParcel(dest, giftCode)
        ParcelUtils.writeToParcel(dest, userId)
        ParcelUtils.writeToParcel(dest, userCategory)
        ParcelUtils.writeToParcel(dest, headFileName)
        ParcelUtils.writeToParcel(dest, giveNum)
        ParcelUtils.writeToParcel(dest, nickName)
        ParcelUtils.writeToParcel(dest, followFlag)
        ParcelUtils.writeToParcel(dest, userRole)
        ParcelUtils.writeToParcel(dest, tendsId)
        ParcelUtils.writeToParcel(dest, newUserFlag)
        ParcelUtils.writeToParcel(dest, warnMsg)
        ParcelUtils.writeToParcel(dest, content)
    }

    companion object {
        @JvmStatic
        fun obtain(
            type: String?,
            channelId: String?,
            giftCode: String?,
            userId: String?,
            userCategory: String?,
            headFileName: String?,
            giveNum: String?,
            nickName: String?,
            followFlag: String?,
            userRole: String?,
            tendsId: String?,
            newUserFlag: String?,
            warnMsg: String?,
            content: String?
        ): MikChatMessage {
            return MikChatMessage().apply {
                this.type = type
                this.channelId = channelId
                this.giftCode = giftCode
                this.userId = userId
                this.userCategory = userCategory
                this.headFileName = headFileName
                this.giveNum = giveNum
                this.nickName = nickName
                this.followFlag = followFlag
                this.userRole = userRole
                this.tendsId = tendsId
                this.newUserFlag = newUserFlag
                this.warnMsg = warnMsg
                this.content = content
            }
        }

        @JvmField
        val CREATOR: Parcelable.Creator<MikChatMessage> =
            object : Parcelable.Creator<MikChatMessage> {
                override fun createFromParcel(source: Parcel): MikChatMessage {
                    return MikChatMessage(source)
                }

                override fun newArray(size: Int): Array<MikChatMessage?> {
                    return arrayOfNulls(size)
                }
            }
    }
}
