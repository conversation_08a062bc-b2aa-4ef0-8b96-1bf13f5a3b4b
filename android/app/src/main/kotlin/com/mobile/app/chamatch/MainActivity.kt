package com.mobile.app.chamatch

import io.flutter.embedding.android.FlutterActivity
import android.os.Bundle
import android.provider.Settings
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

import cn.rongcloud.im.wrapper.flutter.RCIMWrapperPlugin
import io.rong.imlib.model.MessageContent

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.mobile.app.chamatch/device_info"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val list: MutableList<Class<out MessageContent?>> = ArrayList()
        list.add(MikChatMessage::class.java)
        list.add(MikChatAskGiftMessage::class.java)
        list.add(MikChatGiftMessage::class.java)
        val imPlugin: RCIMWrapperPlugin =flutterEngine!!.plugins.get(RCIMWrapperPlugin::class.java) as RCIMWrapperPlugin
        imPlugin!!.engine!!.messageContentClassList = list
    }
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            CHANNEL
        ).setMethodCallHandler { call, result ->
            if (call.method == "getDeviceId") {
                val androidId = UniqueIDUtil.getUniqueID(this@MainActivity)
//                    Settings.Secure.getString(
//                    contentResolver,
//                    Settings.Secure.ANDROID_ID
//                )
                result.success(androidId)
            } else {
                result.notImplemented()
            }
        }
    }
}

