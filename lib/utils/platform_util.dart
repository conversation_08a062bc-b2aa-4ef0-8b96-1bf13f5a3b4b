import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

class PlatformUtil {
  static bool _isWeb() {
    return kIsWeb == true;
  }

  static bool _isAndroid() {
    return _isWeb() ? false : Platform.isAndroid;
  }

  static bool _isIOS() {
    return _isWeb() ? false : Platform.isIOS;
  }

  static bool _isMacOS() {
    return _isWeb() ? false : Platform.isMacOS;
  }

  static bool _isWindows() {
    return _isWeb() ? false : Platform.isWindows;
  }

  static bool _isFuchsia() {
    return _isWeb() ? false : Platform.isFuchsia;
  }

  static bool _isLinux() {
    return _isWeb() ? false : Platform.isLinux;
  }

  static bool get isWeb => _isWeb();

  static bool get isAndroid => _isAndroid();

  static bool get isIOS => _isIOS();

  static bool get isMacOS => _isMacOS();

  static bool get isWindows => _isWindows();

  static bool get isFuchsia => _isFuchsia();

  static bool get isLinux => _isLinux();

  static bool get isMobile => _isAndroid() || _isIOS();

  static String platformName() {
    if (isAndroid) {
      return "android";
    } else if (isIOS)
      return "ios";
    else if (isMacOS)
      return "mac";
    else if (isWindows)
      return "windows";
    else if (isLinux)
      return "linux";
    else if (isFuchsia)
      return "fuchsia";
    else
      return "";
  }

  static Future<String?> getDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      // Android 设备信息
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id; // 获取 Android ID
    } else if (Platform.isIOS) {
      // iOS 设备信息
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor; // 获取 iOS 设备唯一标识符
    } else {
      return null;
    }
  }
}
