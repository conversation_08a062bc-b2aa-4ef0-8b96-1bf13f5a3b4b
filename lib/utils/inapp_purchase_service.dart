import 'package:Chamatch/service/UserInfoService.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_inapp_purchase/flutter_inapp_purchase.dart';
import 'package:get/get.dart';

import '../http/http_request.dart';
import '../model/user_model.dart';
import '../repo/api_hub.dart';

class InAppPurchaseService {
  // 初始化连接
  Future<void> init() async {
    await FlutterInappPurchase.instance.initialize();
  }

  // 结束连接
  Future<void> end() async {
    await FlutterInappPurchase.instance.finalize();
  }

  // 查询订阅商品
  Future<List<IAPItem>> fetchSubscriptions(Set<String> productIds) async {
    try {
      return await FlutterInappPurchase.instance
          .getSubscriptions(productIds.toList());
    } catch (e) {
      print('查询订阅商品失败: $e');
      return [];
    }
  }

  // 查询内购商品
  Future<List<IAPItem>> fetchProducts(Set<String> productIds) async {
    try {
      return await FlutterInappPurchase.instance
          .getProducts(productIds.toList());
    } catch (e) {
      print('查询内购商品失败: $e');
      return [];
    }
  }

  // 发起订阅请求
  Future<void> purchaseSubscription(String productId, String orderId) async {
    try {
      final products = await fetchSubscriptions({productId});
      if (products.isNotEmpty) {
        await FlutterInappPurchase.instance.requestSubscription(productId,
            obfuscatedAccountIdAndroid:
                Get.find<UserInfoService>().user.value?.id,
            obfuscatedProfileIdAndroid: orderId,
            offerTokenIndex: 0);
      } else {
        print('订阅商品未找到: $productId');
      }
    } catch (e) {
      print('订阅失败: $e');
      rethrow;
    }
  }

  // 发起内购请求
  Future<void> purchaseProduct(String productId, String orderId) async {
    try {
      final products = await fetchProducts({productId});
      if (products.isNotEmpty) {
        print(
            '内购商品找到: $productId $orderId ${Get.find<UserInfoService>().user.value?.id}');
        await FlutterInappPurchase.instance.requestPurchase(
          productId,
          obfuscatedAccountId: Get.find<UserInfoService>().user.value?.id,
          obfuscatedProfileIdAndroid: orderId,
        );
      } else {
        print('内购商品未找到: $productId');
      }
    } catch (e) {
      print('内购失败: $e');
      rethrow;
    }
  }

  // 恢复所有购买记录
  Future<List<PurchasedItem>?> restorePurchases() async {
    try {
      return await FlutterInappPurchase.instance.getAvailablePurchases();
    } catch (e) {
      print('恢复购买失败: $e');
      return [];
    }
  }

  // 监听购买更新
  void listenToPurchaseUpdates(
      void Function(PurchasedItem? purchase) onPurchaseUpdated,
      void Function(dynamic error) onPurchaseError) {
    FlutterInappPurchase.purchaseUpdated.listen((purchase) {
      onPurchaseUpdated.call(purchase);
      completePurchase(purchase);
    }, onDone: () {
      debugPrint("purchase done");
    }, onError: (e) {
      debugPrint("purchase error $e");
    });

    FlutterInappPurchase.purchaseError.listen(onPurchaseError);
  }

  // 完成购买
  Future<void> completePurchase(PurchasedItem? purchase) async {
    try {
      if (purchase != null) {
        await FlutterInappPurchase.instance.finishTransaction(purchase!);
        dynamic result = await HttpRequest.request(
            APIHub.order.checkOrder,
            method: Method.POST,
            params: {
              'productId': purchase.productId,
              'purchaseToken': purchase.purchaseToken
            },
            null);
        if ((int.tryParse(result.toString()) ?? 0) >= 0) {
          UserBean model = Get.find<UserInfoService>().user.value!;
          model?.diamond = int.parse(result.toString());
          Get.find<UserInfoService>().setUser(model);
        }
      }
    } catch (e) {
      print('完成购买失败: $e');
    }
  }
}
