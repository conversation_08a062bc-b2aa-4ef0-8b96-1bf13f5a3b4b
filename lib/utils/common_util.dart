import 'dart:io';

import 'package:flutter/services.dart';

class CommonUtil {
  static String formatSize(num bytes, {int fractionDigits = 2}) {
    if (bytes <= 0.0) return '';
    String suffix;
    num result = bytes;
    if (bytes < 1024) {
      suffix = 'B';
    } else if (bytes < 1048576) {
      suffix = 'KB';
      result /= 1024;
    } else if (bytes < 1073741824) {
      suffix = 'MB';
      result /= 1048576;
    } else if (bytes < 1099511627776) {
      suffix = 'GB';
      result /= 1073741824;
    } else {
      suffix = 'TB';
      result /= 1099511627776;
    }
    return '${result.toStringAsFixed(fractionDigits)} $suffix';
  }

  static String formatOnlineStatusText(String? status) {
    switch (status) {
      case '0':
        return 'Offline';
      case '1':
        return 'Online';
      case '2':
        return 'Busy';
      default:
        return 'Unknown';
    }
  }

  static Future<String> getAssetPath(String countryCode) async {
    final String baseName =
        'assets/countries/country_${countryCode.toLowerCase()}';
    final List<String> extensions = ['.png', '.webp'];
    for (final ext in extensions) {
      final String assetPath = '$baseName$ext';
      if (await assetExists(assetPath)) {
        return assetPath;
      }
    }
    return 'assets/countries/country_us.png'; // 返回默认图片路径
  }

  static Future<bool> assetExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      return false;
    }
  }
}
