import 'package:Chamatch/storage/storage.dart';
import 'package:Chamatch/utils/Hive_util.dart';
import 'package:flutter/services.dart';

class DeviceInfo {
  static const platform = MethodChannel('com.mobile.app.chamatch/device_info');

  static Future<String?> getDeviceId() async {
    try {
      final String? deviceId = await platform.invokeMethod('getDeviceId');
      HiveUtil.save(StorageKeys.DEVICE_ID, deviceId);
      return deviceId;
    } on PlatformException catch (e) {
      print("Failed to get device ID: '${e.message}'.");
      return null;
    }
  }
}
