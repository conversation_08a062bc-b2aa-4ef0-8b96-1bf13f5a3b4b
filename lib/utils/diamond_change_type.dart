class DiamondChangeType {
  // 私有构造函数确保只实例化一个对象
  DiamondChangeType._();

  // 单例实例
  static final DiamondChangeType _instance = DiamondChangeType._();

  // 获取单例实例
  factory DiamondChangeType() {
    return _instance;
  }

  // 定义一个 map 来存储所有类型和对应的资源 ID
  final Map<String, String> _typeMap = {
    "0": "Recharge",
    "1": "Video Chat",
    "2": "Private Photo",
    "3": "Gift",
    "4": "Withdraw",
    "5": "Manual Recharge",
    "6": "Reject Cash",
    "7": "Subscribe",
    "8": "Sign In",
    "9": "View Ad",
    "10": "Send Message",
    "11": "Revenue Sharing",
    "12": "Girl referral reward",
    "13": "Rank Reward",
    "14": "Wish Deduct",
    "15": "Admin Give",
    "16": "Task Completed",
    "17": "Level Recharge",
    "18": "Level Weekly Bonus",
    "19": "Invite User",
  };

  // 获取对应的资源 ID
  String get(String type) {
    return _typeMap[type] ?? ""; // 返回对应的值或默认值 0
  }
}
