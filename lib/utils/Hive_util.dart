import 'dart:convert';

import 'package:Chamatch/utils/DeviceInfo.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

class HiveUtil {
  static const String hiveBox = 'hiveBox';

  static Box? _box;

  // 初始化方法
  static Future<void> init() async {
    _box = await Hive.openBox(hiveBox);
  }

  // 保存数据到 Hive 中
  static void save<T>(String key, T value) {
    // var box = await Hive.openBox(hiveBox);
    if (value is String ||
        value is int ||
        value is double ||
        value is bool ||
        value is List ||
        value is Map) {
      // 基本类型直接存储
      _box?.put(key, value);
    } else {
      // 对象类型存储为 JSON 字符串
      _box?.put(key, jsonEncode(value));
    }
  }

  // 从 Hive 中获取数据
  static T? get<T>(String key, {T Function(Map<String, dynamic>)? fromJson}) {
    var data = _box?.get(key);

    if (data == null) {
      return null;
    }
    if (T == String ||
        T == int ||
        T == double ||
        T == bool ||
        T == List ||
        T == Map) {
      // 如果是基本类型，直接返回
      return data as T;
    } else if (data is String && fromJson != null) {
      // 如果是对象类型，反序列化 JSON 字符串
      return fromJson(jsonDecode(data));
    } else {
      throw Exception(
          "Unsupported type or missing fromJson parser for type $T");
    }
  }

  // 删除数据
  static void delete(String boxName, String key) {
    _box?.delete(key);
  }

  // 清空整个 box
  static void clear(String boxName) async {
    _box?.clear();
    await init();
    DeviceInfo.getDeviceId();
  }
}
