import 'dart:convert';
import 'dart:core';

import 'package:Chamatch/model/anchor_file_model.dart';

import 'anchor_Label_model.dart';

class UserBean {
  String? id;
  String? username;
  String? userCode;
  String? nickName;
  String? headFileName;
  String? country;
  String? birthday;
  final String? userCategory;
  int diamond;
  int level;
  int gender;
  final String? vipFlag;
  final String? vipExpiredFlag;
  final String? vipExpireDay;
  final String? token;

  /*Anchor extra param*/
  final int videoPrice;

  final String? onlineStatus;
  final String? status;
  final int incomeDiamond;
  final int unionId;
  final int followNum;
  final int fansNum;
  final String? followFlag;
  final List<AnchorFileBean>? anchorFileList;
  final String? signature;
  final int age;
  final int vipVideoPrice;
  final int levelKey;
  final List<AnchorLabelBean>? anchorLabelList;
  String? userRole;
  List<String>? photos;
  final int nextGradeDiff;
  final int currentGradeStd;
  final int nextGradeStd;
  final int freeRandomMatch;
  final String? hasThirdPay;

  UserBean({
    required this.id,
    required this.username,
    required this.userCode,
    required this.nickName,
    required this.headFileName,
    required this.country,
    required this.birthday,
    required this.userCategory,
    required this.diamond,
    required this.level,
    required this.gender,
    required this.vipFlag,
    required this.vipExpiredFlag,
    required this.vipExpireDay,
    required this.token,
    required this.videoPrice,
    required this.onlineStatus,
    required this.status,
    required this.unionId,
    required this.incomeDiamond,
    required this.followNum,
    required this.fansNum,
    required this.followFlag,
    required this.anchorFileList,
    required this.signature,
    required this.age,
    required this.vipVideoPrice,
    required this.levelKey,
    required this.anchorLabelList,
    required this.userRole,
    required this.photos,
    required this.nextGradeDiff,
    required this.currentGradeStd,
    required this.nextGradeStd,
    required this.freeRandomMatch,
    required this.hasThirdPay,
  });

  factory UserBean.empty() {
    return UserBean(
      id: "",
      username: "",
      userCode: "",
      nickName: "",
      headFileName: "",
      country: "",
      birthday: "",
      diamond: 0,
      gender: 0,
      vipFlag: "",
      vipExpiredFlag: "",
      vipExpireDay: "",
      token: "",
      videoPrice: 0,
      userCategory: '',
      level: 0,
      onlineStatus: '',
      status: '',
      unionId: 0,
      incomeDiamond: 0,
      followNum: 0,
      fansNum: 0,
      followFlag: '',
      anchorFileList: <AnchorFileBean>[],
      signature: '',
      age: 0,
      vipVideoPrice: 0,
      levelKey: 0,
      anchorLabelList: <AnchorLabelBean>[],
      userRole: '',
      photos: <String>[],
      nextGradeDiff: 0,
      currentGradeStd: 0,
      nextGradeStd: 0,
      freeRandomMatch: 0,
      hasThirdPay: '',
    );
  }

  factory UserBean.fromJson(Map<String, dynamic> json) {
    return UserBean(
      id: json["id"],
      username: json["username"],
      userCode: json["userCode"] ?? '',
      nickName: json["nickName"] ?? '',
      headFileName: json["headFileName"],
      country: json["country"],
      birthday: json["birthday"],
      userCategory: json["userCategory"],
      diamond: int.tryParse(json['diamond']?.toString() ?? '0') ?? 0,
      level: int.tryParse(json["level"]?.toString() ?? '0') ?? 0,
      gender: int.tryParse(json["gender"]?.toString() ?? '0') ?? 0,
      vipFlag: json["vipFlag"],
      vipExpiredFlag: json["vipExpiredFlag"],
      vipExpireDay: json["vipExpireDay"],
      token: json["token"],
      videoPrice: int.tryParse(json["videoPrice"]?.toString() ?? '0') ?? 0,
      onlineStatus: json["onlineStatus"],
      status: json["status"],
      incomeDiamond:
          int.tryParse(json["incomeDiamond"]?.toString() ?? '0') ?? 0,
      unionId: int.tryParse(json["unionId"]?.toString() ?? '0') ?? 0,
      followNum: int.tryParse(json["followNum"]?.toString() ?? '0') ?? 0,
      fansNum: int.tryParse(json["fansNum"]?.toString() ?? '0') ?? 0,
      followFlag: json["followFlag"],
      anchorFileList: json["anchorFileList"] != null
          ? List<AnchorFileBean>.from(json["anchorFileList"] == "[]"
              ? []
              : json["anchorFileList"]
                  ?.map((item) => AnchorFileBean.fromJson(item)))
          : [],
      signature: json["signature"],
      age: int.tryParse(json["age"]?.toString() ?? '0') ?? 0,
      vipVideoPrice:
          int.tryParse(json["vipVideoPrice"]?.toString() ?? '0') ?? 0,
      levelKey: int.tryParse(json["levelKey"]?.toString() ?? '0') ?? 0,
      anchorLabelList: json["anchorLabelList"] != null
          ? List<AnchorLabelBean>.from(json["anchorLabelList"] == "[]"
              ? []
              : json["anchorLabelList"]
                  ?.map((item) => AnchorLabelBean.fromJson(item)))
          : [],
      userRole: json["userRole"],
      photos: json["photos"] != null
          ? List<String>.from(
              json["photos"] == "[]" ? [] : json["photos"]?.map((item) => item))
          : [],
      nextGradeDiff:
          int.tryParse(json["nextGradeDiff"]?.toString() ?? '0') ?? 0,
      currentGradeStd:
          int.tryParse(json["currentGradeStd"]?.toString() ?? '0') ?? 0,
      nextGradeStd: int.tryParse(json["nextGradeStd"]?.toString() ?? '0') ?? 0,
      freeRandomMatch: int.tryParse(json["freeRandomMatch"]?.toString() ?? '0') ?? 0,
      hasThirdPay: json["hasThirdPay"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "username": this.username,
      "userCode": this.userCode,
      "nickName": this.nickName,
      "headFileName": this.headFileName,
      "country": this.country,
      "birthday": this.birthday,
      "userCategory": this.userCategory,
      "diamond": this.diamond,
      "level": this.level,
      "gender": this.gender,
      "vipFlag": this.vipFlag,
      "vipExpiredFlag": this.vipExpiredFlag,
      "vipExpireDay": this.vipExpireDay,
      "token": this.token,
      "videoPrice": this.videoPrice,
      "onlineStatus": this.onlineStatus,
      "status": this.status,
      "incomeDiamond": this.incomeDiamond,
      "unionId": this.unionId,
      "followNum": this.followNum,
      "fansNum": this.fansNum,
      "followFlag": this.followFlag,
      "anchorFileList": jsonEncode(this.anchorFileList),
      "signature": this.signature,
      "age": this.age,
      "vipVideoPrice": this.vipVideoPrice,
      "levelKey": this.levelKey,
      "anchorLabelList": jsonEncode(this.anchorLabelList),
      "userRole": this.userRole,
      "photos": jsonEncode(this.photos),
      "nextGradeDiff": jsonEncode(this.nextGradeDiff),
      "currentGradeStd": jsonEncode(this.currentGradeStd),
      "nextGradeStd": jsonEncode(this.nextGradeStd),
      "freeRandomMatch": jsonEncode(this.freeRandomMatch),
      "hasThirdPay": jsonEncode(this.hasThirdPay),
    };
  }

  static List<UserBean> fromListJson(List json) {
    final format = <UserBean>[];
    for (final item in json) {
      format.add(UserBean.fromJson(item));
    }
    return format;
  }
}
