import 'package:Chamatch/model/gift_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'gift_wrapper_model.g.dart';

@JsonSerializable()
class GiftWrapperBean {
  final String giftTypeId;
  final String giftTypeName;
  final List<GiftBean> giftsList;

  GiftWrapperBean(this.giftTypeId, this.giftTypeName, this.giftsList);

  factory GiftWrapperBean.fromJson(Map<String, dynamic> json) => _$GiftWrapperBeanFromJson(json);
  Map<String, dynamic> toJson() => _$GiftWrapperBeanToJson(this);
//
}
