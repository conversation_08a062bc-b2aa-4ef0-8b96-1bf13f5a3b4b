import 'dart:core';

class VideoDeductBean {
  int diamond;
  String enoughFlag;
  int nextTime;

  VideoDeductBean({
    required this.diamond,
    required this.enoughFlag,
    required this.nextTime,
  });

  factory VideoDeductBean.fromJson(Map<String, dynamic> json) {
    return VideoDeductBean(
      diamond: int.parse(json["diamond"].toString()),
      enoughFlag: json["enoughFlag"],
      nextTime: int.parse(json["nextTime"].toString()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "diamond": this.diamond,
      "enoughFlag": this.enoughFlag,
      "nextTime": this.nextTime,
    };
  }
//
}
