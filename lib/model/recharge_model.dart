import 'dart:core';

import 'package:json_annotation/json_annotation.dart';

part 'recharge_model.g.dart';

@JsonSerializable()
class RechargeBean {
  final String? id;
  final int? num;
  final int? giveNum;
  final String? currency;
  final String? price;
  final String? priceId;
  String? localizedPrice;
  final String? productId;
  final String? googleProductId;
  final String? itemName;
  final String? amount;
  final int? diamondNum;
  final String? message;
  final String? priceType;

  /*1-钻石消耗,2-会员订阅,3-全部*/
  final String? discountType;
  final String? discountMessage;

  /*0-无优惠,2-包含优惠*/
  final String? imageUrl;
  final String? payTypeMessage;
  final String? level;
  final int? payMethodId;

  final int? times;
  final String? countdown;

  RechargeBean(
      this.id,
      this.num,
      this.giveNum,
      this.currency,
      this.price,
      this.priceId,
      this.localizedPrice,
      this.productId,
      this.googleProductId,
      this.itemName,
      this.amount,
      this.diamondNum,
      this.message,
      this.priceType,
      this.discountType,
      this.discountMessage,
      this.imageUrl,
      this.payTypeMessage,
      this.level,
      this.payMethodId,
      this.times,
      this.countdown);

  factory RechargeBean.fromJson(Map<String, dynamic> json) =>
      _$RechargeBeanFromJson(json);

  Map<String, dynamic> toJson() => _$RechargeBeanToJson(this);
}
