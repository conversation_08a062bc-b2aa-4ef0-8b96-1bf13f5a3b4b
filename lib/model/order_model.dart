import 'package:Chamatch/model/recharge_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_model.g.dart';

@JsonSerializable()
class OrderBean extends RechargeBean {
  OrderBean(
      super.id,
      super.num,
      super.giveNum,
      super.currency,
      super.price,
      super.priceId,
      super.originPrice,
      super.productId,
      super.googleProductId,
      super.itemName,
      super.amount,
      super.diamondNum,
      super.message,
      super.priceType,
      super.discountType,
      super.discountMessage,
      super.imageUrl,
      super.payTypeMessage,
      super.level,
      super.payMethodId,
      super.times,
      super.countdown);

  factory OrderBean.fromJson(Map<String, dynamic> json) =>
      _$OrderBeanFromJson(json);

  Map<String, dynamic> toJson() => _$OrderBeanToJson(this);
}
