import 'dart:core';

class ConfigurationBean {
  final String? messagePrice;
  final String? randomMatchPrice;
  final String? rechargeDiscountLimit;
  final String? newUserRemain;
  final String? extractBasic;
  final String? lottery_10;
  final String? userVideoPrice;
  final String? firstOfferPrice;
  final String? rechargeDiscountNum;
  final String? anchorPriceLeveD;
  final String? privatePhotoRate;
  final String? faceLeaveTime;
  final String? giftReward;
  final String? extractRate;
  final String? randomMatchRate;
  final String? anchorPrivatePhoto;
  final String? lottery_100;
  final String? faceUploadTime;
  final String? giftRate;
  final String? lottery_20;
  final String? lottery_1;
  final String? videoCallRate;
  final String? sport_3;
  final String? rechargeReward;
  final String? anchorDefaultCallAmount;
  final String? freeTimeRate;
  final String? anchorPriceLevelB;
  final String? sport_2;
  final String? anchorPriceLevelC;
  final String? sport_1;
  final String? anchorPriceLevelE;
  final String? vipVideoCallSpecialOffer;
  final String? vipMatchSpecialOffer;

  ConfigurationBean({
    required this.messagePrice,
    required this.randomMatchPrice,
    required this.rechargeDiscountLimit,
    required this.newUserRemain,
    required this.extractBasic,
    required this.lottery_10,
    required this.userVideoPrice,
    required this.firstOfferPrice,
    required this.rechargeDiscountNum,
    required this.anchorPriceLeveD,
    required this.privatePhotoRate,
    required this.faceLeaveTime,
    required this.giftReward,
    required this.extractRate,
    required this.randomMatchRate,
    required this.anchorPrivatePhoto,
    required this.lottery_100,
    required this.faceUploadTime,
    required this.giftRate,
    required this.lottery_20,
    required this.lottery_1,
    required this.videoCallRate,
    required this.sport_3,
    required this.rechargeReward,
    required this.anchorDefaultCallAmount,
    required this.freeTimeRate,
    required this.anchorPriceLevelB,
    required this.sport_2,
    required this.anchorPriceLevelC,
    required this.sport_1,
    required this.anchorPriceLevelE,
    required this.vipVideoCallSpecialOffer,
    required this.vipMatchSpecialOffer,
  });

  factory ConfigurationBean.fromJson(Map<String, dynamic> json) {
    return ConfigurationBean(
      messagePrice: json["messagePrice"],
      randomMatchPrice: json["randomMatchPrice"],
      rechargeDiscountLimit: json["rechargeDiscountLimit"],
      newUserRemain: json["newUserRemain"],
      extractBasic: json["extractBasic"],
      lottery_10: json["lottery_10"],
      userVideoPrice: json["userVideoPrice"],
      firstOfferPrice: json["firstOfferPrice"],
      rechargeDiscountNum: json["rechargeDiscountNum"],
      anchorPriceLeveD: json["anchorPriceLeveD"],
      privatePhotoRate: json["privatePhotoRate"],
      faceLeaveTime: json["faceLeaveTime"],
      giftReward: json["giftReward"],
      extractRate: json["extractRate"],
      randomMatchRate: json["randomMatchRate"],
      anchorPrivatePhoto: json["anchorPrivatePhoto"],
      lottery_100: json["lottery_100"],
      faceUploadTime: json["faceUploadTime"],
      giftRate: json["giftRate"],
      lottery_20: json["lottery_20"],
      lottery_1: json["lottery_1"],
      videoCallRate: json["videoCallRate"],
      sport_3: json["sport_3"],
      rechargeReward: json["rechargeReward"],
      anchorDefaultCallAmount: json["anchorDefaultCallAmount"],
      freeTimeRate: json["freeTimeRate"],
      anchorPriceLevelB: json["anchorPriceLevelB"],
      sport_2: json["sport_2"],
      anchorPriceLevelC: json["anchorPriceLevelC"],
      sport_1: json["sport_1"],
      anchorPriceLevelE: json["anchorPriceLevelE"],
      vipVideoCallSpecialOffer: json["vipVideoCallSpecialOffer"],
      vipMatchSpecialOffer: json["vipMatchSpecialOffer"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "messagePrice": this.messagePrice,
      "randomMatchPrice": this.randomMatchPrice,
      "rechargeDiscountLimit": this.rechargeDiscountLimit,
      "newUserRemain": this.newUserRemain,
      "extractBasic": this.extractBasic,
      "lottery_10": this.lottery_10,
      "userVideoPrice": this.userVideoPrice,
      "firstOfferPrice": this.firstOfferPrice,
      "rechargeDiscountNum": this.rechargeDiscountNum,
      "anchorPriceLeveD": this.anchorPriceLeveD,
      "privatePhotoRate": this.privatePhotoRate,
      "faceLeaveTime": this.faceLeaveTime,
      "giftReward": this.giftReward,
      "extractRate": this.extractRate,
      "randomMatchRate": this.randomMatchRate,
      "anchorPrivatePhoto": this.anchorPrivatePhoto,
      "lottery_100": this.lottery_100,
      "faceUploadTime": this.faceUploadTime,
      "giftRate": this.giftRate,
      "lottery_20": this.lottery_20,
      "lottery_1": this.lottery_1,
      "videoCallRate": this.videoCallRate,
      "sport_3": this.sport_3,
      "rechargeReward": this.rechargeReward,
      "anchorDefaultCallAmount": this.anchorDefaultCallAmount,
      "freeTimeRate": this.freeTimeRate,
      "anchorPriceLevelB": this.anchorPriceLevelB,
      "sport_2": this.sport_2,
      "anchorPriceLevelC": this.anchorPriceLevelC,
      "sport_1": this.sport_1,
      "anchorPriceLevelE": this.anchorPriceLevelE,
      "vipVideoCallSpecialOffer": this.vipVideoCallSpecialOffer,
      "vipMatchSpecialOffer": this.vipMatchSpecialOffer,
    };
  }
}
