class DiamondHistoryBean {
  final String id;
  final String changeType;
  final int afterNum;
  final int beforeNum;
  final int changeNum;
  final int isIncreasing;
  final String message;
  final String createTime;

  DiamondHistoryBean(
      {required this.id,
      required this.changeType,
      required this.afterNum,
      required this.beforeNum,
      required this.changeNum,
      required this.isIncreasing,
      required this.message,
      required this.createTime,
      required this.updateTime});

  final String updateTime;

  factory DiamondHistoryBean.fromJson(Map<String, dynamic> json) {
    return DiamondHistoryBean(
      id: json["id"],
      changeType: json["changeType"],
      afterNum: int.parse(json["afterNum"].toString()),
      beforeNum: int.parse(json["beforeNum"].toString()),
      changeNum: int.parse(json["changeNum"].toString()),
      isIncreasing: int.parse(json["isIncreasing"].toString()),
      message: json["message"],
      createTime: json["createTime"],
      updateTime: json["updateTime"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "changeType": this.changeType,
      "afterNum": this.afterNum,
      "beforeNum": this.beforeNum,
      "changeNum": this.changeNum,
      "isIncreasing": this.isIncreasing,
      "message": this.message,
      "createTime": this.createTime,
      "updateTime": this.updateTime,
    };
  }

//
}
