class AnchorLabelBean {
  final int id;
  final String labelName;
  final int likeCount;

  AnchorLabelBean({
    required this.id,
    required this.labelName,
    required this.likeCount,
  });

  factory AnchorLabelBean.fromJson(Map<String, dynamic> json) {
    return AnchorLabelBean(
      id: int.tryParse(json["id"]?.toString() ?? '0') ?? 0,
      labelName: json["labelName"],
      likeCount: int.tryParse(json["likeCount"]?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "labelName": this.labelName,
      "likeCount": this.likeCount,
    };
  }
}
