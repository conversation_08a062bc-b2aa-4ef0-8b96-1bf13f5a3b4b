class CountryBean {
  final String id;
  final String countryName;
  final String countryNameEn;
  final String countryCode;

  CountryBean(
      {required this.id,
      required this.countryName,
      required this.countryNameEn,
      required this.countryCode});

  factory CountryBean.fromJson(Map<String, dynamic> json) {
    return CountryBean(
      id: json["id"],
      countryName: json["countryName"],
      countryNameEn: json["countryNameEn"],
      countryCode: json["countryCode"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "countryName": this.countryName,
      "countryNameEn": this.countryNameEn,
      "countryCode": this.countryCode,
    };
  }
}
