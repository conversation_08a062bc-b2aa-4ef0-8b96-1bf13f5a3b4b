// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderBean _$OrderBeanFromJson(Map<String, dynamic> json) => OrderBean(
      json['id'] as String?,
      (json['num'] as num?)?.toInt(),
      (json['giveNum'] as num?)?.toInt(),
      json['currency'] as String?,
      json['price'] as String?,
      json['priceId'] as String?,
      json['localizedPrice'] as String?,
      json['productId'] as String?,
      json['googleProductId'] as String?,
      json['itemName'] as String?,
      json['amount'] as String?,
      (json['diamondNum'] as num?)?.toInt(),
      json['message'] as String?,
      json['priceType'] as String?,
      json['discountType'] as String?,
      json['discountMessage'] as String?,
      json['imageUrl'] as String?,
      json['payTypeMessage'] as String?,
      json['level'] as String?,
      (json['payMethodId'] as num?)?.toInt(),
      (json['times'] as num?)?.toInt(),
      json['countdown'] as String?,
    );

Map<String, dynamic> _$OrderBeanToJson(OrderBean instance) => <String, dynamic>{
      'id': instance.id,
      'num': instance.num,
      'giveNum': instance.giveNum,
      'currency': instance.currency,
      'price': instance.price,
      'priceId': instance.priceId,
      'localizedPrice': instance.localizedPrice,
      'productId': instance.productId,
      'googleProductId': instance.googleProductId,
      'itemName': instance.itemName,
      'amount': instance.amount,
      'diamondNum': instance.diamondNum,
      'message': instance.message,
      'priceType': instance.priceType,
      'discountType': instance.discountType,
      'discountMessage': instance.discountMessage,
      'imageUrl': instance.imageUrl,
      'payTypeMessage': instance.payTypeMessage,
      'level': instance.level,
      'payMethodId': instance.payMethodId,
      'times': instance.times,
      'countdown': instance.countdown,
    };
