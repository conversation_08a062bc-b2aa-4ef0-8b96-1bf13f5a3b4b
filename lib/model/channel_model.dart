import 'dart:core';

class ChannelBean {
  String? anchorId;
  String? channelId;

  ChannelBean({
    required this.anchorId,
    required this.channelId,
  });

  factory ChannelBean.fromJson(Map<String, dynamic> json) {
    return ChannelBean(
      anchorId: json["anchorId"],
      channelId: json["channelId"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "anchorId": this.anchorId,
      "channelId": this.channelId,
    };
  }
//
}
