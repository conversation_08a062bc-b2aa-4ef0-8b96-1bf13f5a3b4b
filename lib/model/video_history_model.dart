import 'dart:core';

class VideoHistoryBean {
  final String? anchorUserId;
  final String? createTime;
  final String? freeFlag;
  final String? headFileName;
  final String? id;
  final String? nickName;
  final String? sourceType;
  final String? videoStatus;
  final String? videoTimeShow;

  VideoHistoryBean({
    required this.anchorUserId,
    required this.createTime,
    required this.freeFlag,
    required this.headFileName,
    required this.id,
    required this.nickName,
    required this.sourceType,
    required this.videoStatus,
    required this.videoTimeShow,
  });

  factory VideoHistoryBean.fromJson(Map<String, dynamic> json) {
    return VideoHistoryBean(
      anchorUserId: json["anchorUserId"],
      createTime: json["createTime"],
      freeFlag: json["freeFlag"],
      headFileName: json["headFileName"],
      id: json["id"],
      nickName: json["nickName"],
      sourceType: json["sourceType"],
      videoStatus: json["videoStatus"],
      videoTimeShow: json["videoTimeShow"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "anchorUserId": this.anchorUserId,
      "createTime": this.createTime,
      "freeFlag": this.freeFlag,
      "headFileName": this.headFileName,
      "id": this.id,
      "nickName": this.nickName,
      "sourceType": this.sourceType,
      "videoStatus": this.videoStatus,
      "videoTimeShow": this.videoTimeShow,
    };
  }
//
}
