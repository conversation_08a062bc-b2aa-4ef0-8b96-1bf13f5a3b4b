import 'dart:core';

class AnchorFileBean {
  final int id;
  final String anchorId;
  final String fileType;
  final String fileUrl;
  final String fileName;
  final String thumbnail;
  final String status;
  final String isLock;

  AnchorFileBean({
    required this.id,
    required this.anchorId,
    required this.fileType,
    required this.fileUrl,
    required this.fileName,
    required this.thumbnail,
    required this.status,
    required this.isLock,
  });

  factory AnchorFileBean.fromJson(Map<String, dynamic> json) {
    return AnchorFileBean(
      id: int.tryParse(json["id"]?.toString() ?? '0') ?? 0,
      anchorId: json["anchorId"],
      fileType: json["fileType"],
      fileUrl: json["fileUrl"],
      fileName: json["fileName"],
      thumbnail: json["thumbnail"],
      status: json["status"],
      isLock: json["isLock"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "anchorId": this.anchorId,
      "fileType": this.fileType,
      "fileUrl": this.fileUrl,
      "fileName": this.fileName,
      "thumbnail": this.thumbnail,
      "status": this.status,
      "isLock": this.isLock,
    };
  }
}
