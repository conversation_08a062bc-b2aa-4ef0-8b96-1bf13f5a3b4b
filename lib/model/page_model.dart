class PageBean<T> {
  final int current;
  final int size;
  final int total;
  final List<T> records;

  PageBean(this.current, this.size, this.total, this.records);

  factory PageBean.fromJson(Map<String, dynamic> map, T Function(Map<String, dynamic>) fromJson) {
    return PageBean<T>(
      map['current'] as int,
      map['size'] as int,
      map['total'] as int,
      (map['records'] as List).map((item) => from<PERSON>son(item as Map<String, dynamic>)).toList(),
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJson) {
    return {
      'current': current,
      'size': size,
      'total': total,
      'records': records.map((item) => toJson(item)).toList(),
    };
  }
}
