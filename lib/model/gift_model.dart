import 'dart:core';

class GiftBean {
  final int id;
  final String? giftName;
  final int giftPrice;
  final String? giftIcon;
  final int? num;
  final String? giftCode;

  GiftBean({
    required this.id,
    required this.giftName,
    required this.giftPrice,
    required this.giftIcon,
    required this.num,
    required this.giftCode,
  });

  factory GiftBean.fromJson(Map<String, dynamic> json) {
    return GiftBean(
      id: int.parse((json["id"] ?? "0").toString()),
      giftName: json["giftName"],
      giftPrice: int.parse(json["giftPrice"].toString()),
      giftIcon: json["giftIcon"],
      num: int.parse((json["num"] ?? "0").toString()),
      giftCode: json["giftCode"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": this.id,
      "giftName": this.giftName,
      "giftPrice": this.giftPrice,
      "giftIcon": this.giftIcon,
      "num": this.num,
      "giftCode": this.giftCode,
    };
  }

  static List<GiftBean> fromListJson(List json) {
    final format = <GiftBean>[];
    for (final item in json) {
      format.add(GiftBean.fromJson(item));
    }
    return format;
  }
}
