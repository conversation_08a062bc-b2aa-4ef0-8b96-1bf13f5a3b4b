import 'dart:ffi';

class MatchCredentialBean {
  /*是否过了新用户领取日期*/
  final String dayFreeFreshExpired;

  /*刷新匹配卡倒计时时间戳 该值为-1时无需倒计时或不能领取*/
  final int dayFreeFreshTimeStamp;

/*当前剩余免费随机匹配次数*/
  final int freeRandomMatch;

  factory MatchCredentialBean.empty() {
    return MatchCredentialBean(
        dayFreeFreshExpired: "", dayFreeFreshTimeStamp: 0, freeRandomMatch: 0);
  }

  MatchCredentialBean(
      {required this.dayFreeFreshExpired,
      required this.dayFreeFreshTimeStamp,
      required this.freeRandomMatch});

  factory MatchCredentialBean.fromJson(Map<String, dynamic> json) {
    return MatchCredentialBean(
      dayFreeFreshExpired: json["dayFreeFreshExpired"],
      dayFreeFreshTimeStamp:
          int.parse(json["dayFreeFreshTimeStamp"].toString()),
      freeRandomMatch: int.parse(json["freeRandomMatch"].toString()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "dayFreeFreshExpired": this.dayFreeFreshExpired,
      "dayFreeFreshTimeStamp": this.dayFreeFreshTimeStamp,
      "freeRandomMatch": this.freeRandomMatch,
    };
  }

//
}
