import 'package:Chamatch/controller/user_controller.dart';
import 'package:Chamatch/model/country_model.dart';
import 'package:Chamatch/utils/common_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

typedef OnItemTapCallback = void Function(CountryBean bean);

class CountryManager {
  static void showCountryDialog(BuildContext context, OnItemTapCallback onTap) {
    var controller = Get.find<UserController>();
    controller.getCountryList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Stack(
          children: [
            Positioned.fill(
                child: ColoredBox(
              color: Colors.black, // 颜色
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8)),
                ),
                width: MediaQuery.of(context).size.width,
                height: 550,
              ),
            )),
            Image.asset("assets/bg_bottom_sheet.png"),
            Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        width: 80,
                        height: 5,
                        decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      const Text("Select Country",
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white60,
                            fontWeight: FontWeight.bold,
                          )),
                      const SizedBox(height: 20),
                      SizedBox(
                          height: 400,
                          child: Obx(() => ListView.builder(
                              itemCount: controller.countryList.length,
                              itemBuilder: (context, index) {
                                return ListTile(
                                  leading: FutureBuilder<String>(
                                    future: CommonUtil.getAssetPath(controller
                                        .countryList[index].countryCode),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                              ConnectionState.done &&
                                          snapshot.hasData) {
                                        return Image.asset(
                                          snapshot.data!,
                                          fit: BoxFit.cover,
                                          width: 30,
                                        );
                                      } else {
                                        return Image.asset(
                                          'assets/countries/country_us.png',
                                          // 默认图片
                                          fit: BoxFit.cover,
                                          width: 30,
                                        );
                                      }
                                    },
                                  ),
                                  title: Text(
                                    controller.countryList[index].countryNameEn,
                                    style: const TextStyle(
                                        fontSize: 14, color: Colors.white),
                                  ),
                                  onTap: () {
                                    onTap.call(controller.countryList[index]);
                                    Navigator.pop(context);
                                  },
                                );
                              })))
                    ])))
          ],
        );
      },
    );
  }
}
