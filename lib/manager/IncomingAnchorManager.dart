import 'dart:async';
import 'dart:math';

import 'package:Chamatch/utils/log.dart';

class IncomingAnchorManager {
  // 私有构造函数
  IncomingAnchorManager._privateConstructor();

  // 单例实例
  static final IncomingAnchorManager _instance =
      IncomingAnchorManager._privateConstructor();

  // 获取单例实例的工厂方法
  factory IncomingAnchorManager() {
    return _instance;
  }

  // 配置参数
  int _aibDurationTime = 0; // 计时时间
  int _aibFirstIntervalTime = 45; // 首次延迟时间
  int _aibDefaultIntervalTime = 50; // 默认间隔时间
  int _aibGapRandomMaxNum = 15; // 随机时间最大值
  int _aibIntervalTimeCoefficient = 30; // 时间间隔系数
  int _aibCancelCount = 0; // 用户取消次数
  int _intervalTime = 45; // 当前间隔时间

  // 定时器
  Timer? _timer;
  bool _isPaused = false;
  void Function()? _loopCallback; // 保存的回调

  // 初始化配置
  void _resetDefaultConfig() {
    _aibDurationTime = 0;
    _aibCancelCount = 0;
    _intervalTime = _aibFirstIntervalTime;
  }

  // 开始轮询
  void startLoop(void Function() callback) {
    // 如果已经运行中，直接返回
    if (_timer?.isActive == true) return;
    _loopCallback = callback; // 保存回调
    _resetDefaultConfig();
    _startTimer();
  }

  // 内部方法：启动定时器
  void _startTimer() {
    if (_loopCallback == null) return; // 确保回调存在
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // LogUtil.e("vvvvvvvv $_aibDurationTime  $_intervalTime");
      if (_isPaused) {
        timer.cancel();
        return;
      }

      _aibDurationTime++;
      if (_aibDurationTime >= _intervalTime) {
        _loopCallback!();
        recalculateIntervalTime();
        _aibDurationTime = 0; // 重置计时时间
      }
    });
  }

  // 暂停轮询
  void pauseLoop() {
    _isPaused = true;
  }

  // 恢复轮询
  void resumeLoop() {
    if (!_isPaused) return; // 如果未暂停，直接返回
    _isPaused = false;
    _startTimer(); // 直接重新启动定时器
  }

  // 停止轮询
  void stopLoop() {
    _timer?.cancel();
    _timer = null;
  }

  // 重新计算下次请求时间
  void recalculateIntervalTime() {
    final random = Random();
    _intervalTime = _aibDurationTime +
        _aibDefaultIntervalTime +
        random.nextInt(_aibGapRandomMaxNum) +
        _aibCancelCount * _aibIntervalTimeCoefficient;

    LogUtil.i("vvvvvvv111111 $_intervalTime");
  }

  // 增加取消次数
  void addCancelCount() {
    _aibCancelCount++;
  }

  // 更新配置
  void updateConfig({
    int? firstIntervalTime,
    int? defaultIntervalTime,
    int? gapRandomMaxNum,
    int? intervalTimeCoefficient,
  }) {
    if (firstIntervalTime != null) _aibFirstIntervalTime = firstIntervalTime;
    if (defaultIntervalTime != null)
      _aibDefaultIntervalTime = defaultIntervalTime;
    if (gapRandomMaxNum != null) _aibGapRandomMaxNum = gapRandomMaxNum;
    if (intervalTimeCoefficient != null)
      _aibIntervalTimeCoefficient = intervalTimeCoefficient;
  }
}
