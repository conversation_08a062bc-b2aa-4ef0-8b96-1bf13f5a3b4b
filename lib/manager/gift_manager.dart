import 'package:Chamatch/manager/payment_manager.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/widget/gift_panel_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../model/gift_model.dart';

typedef OnSendCallback = void Function(GiftBean bean);

class GiftManager {
  static void showGiftDialog(BuildContext context,
      {required OnSendCallback callback}) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => GiftPanelWidget(
            callback: (item) => {
                  if (item.giftPrice >
                      (Get.find<UserInfoService>().user.value?.diamond ?? 0))
                    {PaymentManager.showRechargeDialog(context)}
                  else
                    {callback.call(item), Navigator.of(context).pop()}
                }));
  }
}
