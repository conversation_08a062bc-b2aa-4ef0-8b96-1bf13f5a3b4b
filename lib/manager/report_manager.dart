import 'package:facebook_app_events/facebook_app_events.dart';

class ReportManager {
  // 初始化 Facebook SDK
  static final FacebookAppEvents _facebookAppEvents = FacebookAppEvents();

  // 上报事件
  static Future<void> reportEvent(String eventName,
      {Map<String, dynamic>? parameters}) async {
    try {
      _facebookAppEvents.logEvent(
        name: eventName,
        parameters: parameters,
      );
      print('Event reported: $eventName');
    } catch (e) {
      print("Error reporting event: $e");
    }
  }

  // 上报页面访问事件（示例）
  static Future<void> reportPageView(String pageName) async {
    await reportEvent("Page_View", parameters: {"page": pageName});
  }

  // 上报购买事件（示例）
  static Future<void> reportPurchaseEvent(
      String productName, double price) async {
    await reportEvent("Purchase", parameters: {
      "product": productName,
      "price": price,
      "currency": "USD" // 可以根据需要修改货币类型
    });
  }

  // 上报自定义事件（可以根据需求自定义）
  static Future<void> reportCustomEvent(String customEventName,
      {Map<String, dynamic>? parameters}) async {
    await reportEvent(customEventName, parameters: parameters);
  }
}
