import 'package:Chamatch/controller/user_controller.dart';
import 'package:Chamatch/manager/country_manager.dart';
import 'package:Chamatch/manager/report_manager.dart';
import 'package:Chamatch/model/recharge_model.dart';
import 'package:Chamatch/pages/mine/sub/wallet/controllers/wallet_controller.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/toast_util.dart';
import 'package:Chamatch/widget/item_recharge_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../utils/common_util.dart';
import '../utils/inapp_purchase_service.dart';
import '../widget/item_payment_method_widget.dart';

class PaymentManager {
  static void showRechargeDialog(BuildContext context) {
    var controller = Get.find<WalletController>();
    var userBean = Get.find<UserInfoService>();
    controller.fetchRechargeList();
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) => Stack(
              children: [
                Positioned.fill(
                    child: ColoredBox(
                  color: const Color(0xff082110),
                  child: Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8)),
                    ),
                    width: MediaQuery.of(context).size.width,
                    height: 550,
                  ),
                )),
                Image.asset("assets/bg_bottom_sheet.png"),
                Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: SingleChildScrollView(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                          const SizedBox(
                            height: 20,
                          ),
                          Container(
                            width: 80,
                            height: 5,
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/ic_diamond.png",
                                width: 30,
                              ),
                              Text(
                                "${userBean.user.value?.diamond}",
                                style: const TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          const Text("Available Diamonds",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 16)),
                          const SizedBox(height: 15),
                          SizedBox(
                              height: 400,
                              child: Obx(() => ListView.builder(
                                  itemCount: controller.rechargeList.length,
                                  itemBuilder: (context, index) {
                                    return ItemRechargeWidget(
                                        bean: controller.rechargeList[index],
                                        itemCallback: (item) => {
                                              showPaymentMethodDialog(
                                                  context, item)
                                            });
                                  })))
                        ])))
              ],
            ));
  }

  static void showPaymentMethodDialog(BuildContext context, RechargeBean item) {
    var controller = Get.find<WalletController>();
    var userBean = Get.find<UserInfoService>();
    var currentCountry = (userBean.user.value?.country ?? "ALL").obs;
    var allowThirdPay = (userBean.user.value?.hasThirdPay);

    if (allowThirdPay == "0") {
      _createOrder(context, item);
      return;
    }

    controller.fetchPaymentList(item.itemName ?? "", item.discountType ?? "",
        currentCountry?.value ?? "ALL");

    ever<String>(currentCountry, (newCountry) {
      controller.fetchPaymentList(
        item.itemName ?? "",
        item.discountType ?? "",
        newCountry,
      );
    });

    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) => Stack(
              children: [
                Positioned.fill(
                    child: ColoredBox(
                  color: Colors.black, // 颜色
                  child: Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8)),
                    ),
                    width: MediaQuery.of(context).size.width,
                    height: 550,
                  ),
                )),
                Image.asset("assets/bg_bottom_sheet.png"),
                Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: SingleChildScrollView(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                          const SizedBox(
                            height: 20,
                          ),
                          Container(
                            width: 80,
                            height: 5,
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/ic_diamond.png",
                                width: 30,
                              ),
                              Text(
                                "${item.itemName}",
                                style: const TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: const Text("Country")),
                          const SizedBox(height: 10),
                          InkWell(
                              onTap: () {
                                CountryManager.showCountryDialog(context,
                                    (item) {
                                  currentCountry?.value = item.countryCode;
                                  currentCountry?.refresh();
                                });
                              },
                              child: Obx(() => Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(10))),
                                    child: Row(
                                      children: [
                                        const SizedBox(width: 10),
                                        FutureBuilder<String>(
                                          future: CommonUtil.getAssetPath(
                                              currentCountry?.value ?? ""),
                                          builder: (context, snapshot) {
                                            if (snapshot.connectionState ==
                                                    ConnectionState.done &&
                                                snapshot.hasData) {
                                              return Image.asset(
                                                snapshot.data!,
                                                fit: BoxFit.cover,
                                                width: 30,
                                              );
                                            } else {
                                              return Image.asset(
                                                'assets/countries/country_us.png',
                                                // 默认图片
                                                fit: BoxFit.cover,
                                                width: 30,
                                              );
                                            }
                                          },
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                            child: Text(
                                          currentCountry?.value ?? "",
                                          style: const TextStyle(fontSize: 16),
                                        )),
                                        const Icon(
                                          Icons.arrow_forward_ios_rounded,
                                          size: 15,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 15),
                                      ],
                                    ),
                                  ))),
                          const SizedBox(height: 20),
                          SizedBox(
                              height: 400,
                              child: Obx(() => ListView.builder(
                                  itemCount: controller.paymentList.length,
                                  itemBuilder: (context, index) {
                                    return ItemPaymentMethodWidget(
                                        bean: controller.paymentList[index],
                                        localizedPrice: item.localizedPrice,
                                        itemCallback: (item) =>
                                            {_createOrder(context, item)});
                                  })))
                        ])))
              ],
            ));
  }
}

void _createOrder(BuildContext context, RechargeBean item) {
  var controller = Get.find<WalletController>();
  var userController = Get.find<UserController>();
  var userBean = Get.find<UserInfoService>();
  ToastUtil.loading();
  controller.createOrder(
      item.id ?? "",
      item.discountType ?? "",
      (order) => {
            _launchGooglePurchase(context, item.priceType, order, () {
              userController.getUserDetail(userBean.user.value?.id ?? "");
            }),
            ToastUtil.dismiss(),
          });
}

void _launchGooglePurchase(BuildContext context, String? productType,
    RechargeBean item, Function callback) {
  final inAppPurchaseService = InAppPurchaseService()..init();

  if (productType == "1") {
    inAppPurchaseService.purchaseProduct(
        item.googleProductId ?? item.googleProductId ?? "", item.id ?? "");
  } else {
    inAppPurchaseService.purchaseSubscription(
        item.googleProductId ?? item.googleProductId ?? "", item.id ?? "");
  }
  inAppPurchaseService.listenToPurchaseUpdates((onPurchaseUpdated) {
    ReportManager.reportPurchaseEvent(
        "${item?.diamondNum}", double.tryParse(item?.amount ?? "") ?? 0);
    showPaymentResultDialog(
        context, () => {callback.call(), Navigator.of(context).pop()});
  }, (onPurchaseError) {});
}

void showPaymentResultDialog(BuildContext context, Function onTap) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: const Color(0xff0f0f0f),
        insetPadding: const EdgeInsets.symmetric(horizontal: 30),
        content: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 15),
              Image.asset(
                "assets/ic_payment_success.png",
                width: 77,
              ),
              const SizedBox(height: 10),
              const Text(
                "Payment Successful",
                style: TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
              ),
              const Text(
                "We have received your recharge order system is processing",
                style: TextStyle(
                    fontSize: 16,
                    color: Color(0xff9f9ca6),
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭弹框
              onTap.call();
            },
            child: const Text(
              '确认',
              style: TextStyle(color: Colors.green),
            ),
          ),
        ],
      );
    },
  );
}
