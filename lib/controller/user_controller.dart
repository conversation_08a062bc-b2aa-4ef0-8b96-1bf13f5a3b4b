import 'package:Chamatch/http/http_request.dart';
import 'package:Chamatch/model/country_model.dart';
import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/repo/api_hub.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:get/get.dart';

import '../model/gift_model.dart';
import '../model/gift_wrapper_model.dart';
import '../storage/storage.dart';
import '../utils/Hive_util.dart';
import '../utils/toast_util.dart';

class UserController extends GetxController {
  var userBean = UserBean.empty().obs;
  final userInfoService = Get.find<UserInfoService>();
  final countryList = <CountryBean>[].obs;

  getUserDetail(String id) async {
    userBean.value = await HttpRequest.request(
        APIHub.user.userDetail,
        params: {"id": id},
        (p0) => UserBean.fromJson(p0));
    userInfoService.setUser(userBean.value);
  }

  getGiftList() {
    return HttpRequest.request<GiftWrapperBean>(
        APIHub.user.giftList,
        method: Method.POST,
        (p0) => GiftWrapperBean.fromJson(p0));
  }

  upload(String path) {
    return HttpRequest.uploadFile(APIHub.user.uploadFile, path);
  }

  getCountryList() async {
    ToastUtil.loading();
    countryList.value = await HttpRequest.request<CountryBean>(
        APIHub.user.countryList,
        method: Method.GET,
        (p0) => CountryBean.fromJson(p0));
    ToastUtil.dismiss();
  }

  updateProfile(UserBean params) async {
    HttpRequest.request(
            APIHub.user.updateProfile,
            method: Method.POST,
            params: params.toJson(),
            (p0) => String.fromEnvironment(p0, defaultValue: p0))
        .then((onValue) => {
              HiveUtil.save(StorageKeys.TOKEN_KEY, onValue),
              getUserDetail(userInfoService.user?.value?.id ?? "")
            });
  }
}
