import 'dart:convert';
import 'dart:io';

import 'package:Chamatch/storage/storage.dart';
import 'package:Chamatch/utils/Hive_util.dart';
import 'package:Chamatch/utils/log.dart';
import 'package:Chamatch/utils/platform_util.dart';
import 'package:Chamatch/utils/toast_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';

import '../utils/DeviceInfo.dart';
import '../utils/constants.dart';
import 'extend_http_client.dart';

enum Method { GET, POST }

class HttpRequest {
  static final SafeHttpClient _httpClient = SafeHttpClient(http.Client());

  static Future<dynamic> request<T>(String url, T Function(dynamic)? fromJson,
      {Method method = Method.GET,
      Map<String, dynamic>? params,
      Map<String, dynamic>? queryData,
      Function(Object)? exception}) async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final deviceInfoPlugin = DeviceInfoPlugin();
    String model = '';
    String manufacturer = '';
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfoPlugin.androidInfo;
      model = androidInfo.model;
      manufacturer = androidInfo.manufacturer;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfoPlugin.iosInfo;
      model = iosInfo.utsname.machine;
      manufacturer = 'Apple';
    }
    var headers = {
      'appName': packageInfo.appName,
      'platform': Platform.operatingSystem,
      'version': packageInfo.version,
      'buildNumber': packageInfo.buildNumber,
      'systemVersion': Platform.operatingSystemVersion,
      'Content-Type': 'application/json',
      'appos': PlatformUtil.platformName(),
      'model': model,
      'manufacturer': manufacturer,
      'Blade-Auth': HiveUtil.get<String>(StorageKeys.TOKEN_KEY) ?? "",
      'X-versionCode': packageInfo.buildNumber,
      'X-versionName': packageInfo.version,
      'X-pkg': packageInfo.packageName,
      'X-model': model,
      'X-deviceId': HiveUtil.get<String>(StorageKeys.DEVICE_ID) ??
          (await DeviceInfo.getDeviceId()).toString(),
      'X-deviceType': PlatformUtil.platformName()
    };

    http.Response? response;

    String errorMessage = '';
    try {
      if (method == Method.GET) {
        response = await _httpClient.get(Uri.https(Urls.hostname, url, params),
            headers: headers);
      }
      if (method == Method.POST) {
        response = await _httpClient.post(
            Uri.https(Urls.hostname, url, queryData),
            headers: headers,
            body: jsonEncode(params),
            encoding: Encoding.getByName('utf-8'));
      }

      debugPrint(
          "request url： ${response?.request?.url} \nheaders：${response?.headers} \nparams：$params");

      Utf8Decoder decoder = const Utf8Decoder();
      var content = jsonDecode(decoder.convert(response!.bodyBytes));
      LogUtil.i(content);
      errorMessage = content['msg'];
      var data = content['data'];
      if (content['code'] == 200) {
        if (fromJson != null) {
          if (data is List) {
            return List<T>.from(data.map((e) => fromJson(e)));
          } else {
            return fromJson(data);
          }
        } else {
          return data;
        }
      } else if (content['code'] == 401) {
        return data;
      } else {
        ToastUtil.error(errorMessage);
        exception?.call(Exception(errorMessage));
        return data;
      }
    } catch (e) {
      // debugPrint(e.toString());
      LogUtil.e(e.toString());
      errorMessage = errorMessage ?? 'server internal exception';
      exception?.call(e);
      if (errorMessage.isNotEmpty) {
        ToastUtil.error(errorMessage);
      }
    }
    throw Exception(errorMessage);
  }

  static Future<String?> uploadFile(String url, String filePath,
      {Map<String, String>? headers, Function(Object)? exception}) async {
    try {
      ToastUtil.loading(msg: "Uploading file");
      var request =
          http.MultipartRequest('POST', Uri.https(Urls.hostname, url));
      request.headers.addAll(headers ?? {});
      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      debugPrint("upload file response: ${response.body}");

      if (response.statusCode == 200) {
        Utf8Decoder decoder = const Utf8Decoder();
        var content = jsonDecode(decoder.convert(response.bodyBytes));
        LogUtil.i(content);
        ToastUtil.dismiss();

        if (content['code'] == 200) {
          return content['data']; // 假设返回的 data 是文件路径
        } else {
          ToastUtil.error(content['msg'] ?? 'Upload failed');
        }
      } else {
        ToastUtil.error('Upload failed with status: ${response.statusCode}');
      }
    } catch (e) {
      LogUtil.e(e.toString());
      exception?.call(e);
      ToastUtil.error('Upload exception: $e');
    }
    return null;
  }
}
