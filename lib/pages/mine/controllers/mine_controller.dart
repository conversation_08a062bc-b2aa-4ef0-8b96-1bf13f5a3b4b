import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/repo/api_hub.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/storage/storage.dart';
import 'package:Chamatch/utils/Hive_util.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../http/http_request.dart';

class MineController extends GetxController {
  final userInfoService = Get.find<UserInfoService>();

  @override
  onInit() async {
    super.onInit();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    if (userInfoService.user.value?.id?.isNotEmpty == true) {
      _getUserDetail(userInfoService.user.value?.id ?? '');
    }
  }

  _getUserDetail(String id) async {
    UserBean result = await HttpRequest.request(
        APIHub.user.userDetail,
        params: {"id": id},
        (p0) => UserBean.fromJson(p0));
    userInfoService.setUser(result);
  }
}
