import 'package:Chamatch/manager/payment_manager.dart';
import 'package:Chamatch/model/pair.dart';
import 'package:Chamatch/model/recharge_model.dart';
import 'package:Chamatch/pages/mine/sub/wallet/controllers/wallet_controller.dart';
import 'package:Chamatch/utils/constants.dart';
import 'package:Chamatch/utils/inapp_purchase_service.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher_string.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State<StatefulWidget> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  int selectedIndex = 1; // 默认选中 "Month" 选项
  WalletController controller = Get.put(WalletController());
  InAppPurchaseService? inAppPurchaseService;
  List<RechargeBean> rechargeList = <RechargeBean>[].obs;

  @override
  void initState() {
    super.initState();
    inAppPurchaseService = InAppPurchaseService()..init();
    controller.rechargeList.listen((onData) {
      if (rechargeList.isNotEmpty) rechargeList.clear();
      rechargeList.addAll(onData);
      inAppPurchaseService
          ?.fetchSubscriptions(
              onData.map((item) => item.productId ?? "").toSet())
          .then((onValue) => {
                setState(() {
                  onValue.forEach((action) {
                    rechargeList
                        .firstWhereOrNull(
                            (it) => it.productId == action.productId)
                        ?.localizedPrice = action.localizedPrice;
                  });
                })
              });
    });

    controller.fetchRechargeList(priceType: "2");
  }

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Subscription",
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            // 顶部订阅选项
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                // padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white.withOpacity(0.5)),
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0x1A1d1d1d), // 背景透明度为10%
                ),
                child: Obx(() => Column(
                      children: List.generate(rechargeList.length, (index) {
                        final option = rechargeList[index];
                        return _buildSubscriptionOption(
                          option,
                          selectedIndex == index,
                          onTap: () {
                            setState(() {
                              selectedIndex = index;
                            });
                          },
                        );
                      }),
                    )),
              ),
            ),
            const SizedBox(height: 20),
            // 中间 Privileges 区域
            const Text(
              "7 Exclusive Privilege",
              style: TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.bold,
                fontFamily: "Helvetica",
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: const Color(0xFF122A1A),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ..._buildPrivileges(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            // 底部协议
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Center(
                child: RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                    style: const TextStyle(color: Colors.grey, fontSize: 12),
                    children: [
                      const TextSpan(
                        text:
                            "Payment will be charged to your Google Play Store account at confirmation of purchase. You can turn off auto-renewal at least 24 hours before the end of your current subscription period. We do not offer a free trial for subscription content. The subscription period is one month, and your subscription will automatically renew within the same package period at the same price. Subscriptions can be cancelled at any time. Cancel your subscription by clicking here: ",
                      ),
                      TextSpan(
                        text: "Cancel subscription \n\n",
                        style: const TextStyle(
                          color: Color(0xFFC03D3D),
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () async {
                            const String packageName =
                                'com.mobile.app.chamatchpro'; // 替换为你的包名
                            launchUrlString(
                                "https://play.google.com/store/account/subscriptions?package=$packageName");
                          },
                      ),
                      const TextSpan(
                        text: "\nFor more information, please visit our ",
                      ),
                      TextSpan(
                        text: "User Agreement",
                        style: const TextStyle(
                          color: Color(0xFF10DA23),
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            launchUrlString(Urls.userAgreement);
                          },
                      ),
                      const TextSpan(
                        text: " and ",
                      ),
                      TextSpan(
                        text: "Privacy Policy.",
                        style: const TextStyle(
                          color: Color(0xFF10DA23),
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            launchUrlString(Urls.privacyPolicy);
                          },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Renew Now 按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: InkWell(
                onTap: () {
                  PaymentManager.showPaymentMethodDialog(
                      context, rechargeList[selectedIndex]);
                },
                child: Container(
                  height: 44,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF47FFDA), Color(0xFF10DA23)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      )),
                  child: const Text(
                    "Renew Now",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Expired 日期
            if (controller.userBean?.value?.vipExpireDay?.isNotEmpty == true)
              Text(
                "Expired on ${controller.userBean?.value?.vipExpireDay}",
                style: const TextStyle(
                  color: Color(0xFF10DA23),
                  fontSize: 12,
                ),
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Subscription option widget
  Widget _buildSubscriptionOption(RechargeBean bean, bool selected,
      {required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        decoration: BoxDecoration(
            border: selected
                ? Border.all(color: const Color(0xFF10DA23), width: 2)
                : null,
            borderRadius: BorderRadius.circular(8),
            color:
                selected ? const Color(0x1A10DA23) : const Color(0x00000000)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "${bean.itemName}",
              style: TextStyle(
                color: selected ? Colors.white : Colors.grey[300],
                fontWeight: FontWeight.bold,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  bean.localizedPrice ?? "${bean.currency}${bean.price}",
                  style: TextStyle(
                    color: selected ? Colors.white : Colors.grey[300],
                  ),
                ),
                if (bean.discountMessage != null)
                  Text(
                    "${bean.discountMessage}",
                    style: const TextStyle(
                      color: Color(0xFF10DA23),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build privilege list
  List<Widget> _buildPrivileges() {
    final privileges = [
      const Pair(
          "Video Call Special Offer", "VIP gets a 20% discount per minute"),
      const Pair("Unlimited Messages ", " Free times to send messages"),
      const Pair("Check-in Bonus ", " Get up to 5,000 diamonds"),
      const Pair("Match Special Offer ", " VIP only needs 15 diamonds/match"),
      const Pair("Who see me ", " Unlock privilege to see \"who see me\" list"),
      const Pair("Photo and voice messages ",
          " Free times to send photo and voice messages"),
      const Pair("Secret images/videos ",
          " Unlock privilege to see secret images and videos"),
    ];

    return List.generate(privileges.length, (index) {
      final privilege = privileges[index];
      return ListTile(
          leading: Container(
            width: 23,
            height: 23,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white60.withOpacity(0.2),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Text(
              "${index + 1}",
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          title: Text(
            "${privilege.first}",
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
          subtitle: Text("${privilege.second}",
              style: TextStyle(
                  color: Colors.white.withOpacity(0.5), fontSize: 12)));
    });
  }
}
