import 'package:Chamatch/model/diamond_history_model.dart';
import 'package:Chamatch/utils/diamond_change_type.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../widget/refresh.dart';
import 'wallet/controllers/wallet_controller.dart';

class DiamondHistoryPage extends StatefulWidget {
  const DiamondHistoryPage({super.key});

  @override
  State<StatefulWidget> createState() => _DiamondDetailPageState();
}

class _DiamondDetailPageState extends State<DiamondHistoryPage> {
  WalletController controller = Get.put(WalletController());
  int currentPage = 1;

  @override
  void initState() {
    super.initState();
    _fetchData(true);
  }

  _fetchData(bool refresh) {
    currentPage = refresh ? 1 : currentPage + 1;
    controller.fetchDiamondHistory(currentPage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 0,
          leading: const BackButton(color: Colors.white),
          title: const Text(
            'Diamond History',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          centerTitle: true,
        ),
        body: SafeArea(
            child: Refresh(
                onRefresh: () => {_fetchData(true)},
                onLoad: () => {_fetchData(false)},
                child: Obx(() => controller.diamondHistoryList.isEmpty
                    ? Center(
                        child: Image.asset("assets/ic_no_data.png", width: 50))
                    : ListView.builder(
                        itemCount: controller.diamondHistoryList.length,
                        itemBuilder: (context, index) => _buildItem(
                            controller.diamondHistoryList[index]))))));
  }

  Widget _buildItem(
    DiamondHistoryBean bean,
  ) {
    return Container(
      color: const Color(0xFF1A1A1A),
      padding: const EdgeInsets.only(left: 0, right: 12, top: 10, bottom: 12),
      child: Row(
        children: [
          // Left section with dot and title
          Expanded(
            child: Row(
              children: [
                // Active dot indicator
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    // color: isActive ? Colors.green : Colors.grey,
                  ),
                ),
                Image.asset("assets/ic_diamond.png", width: 30, height: 30),
                const SizedBox(
                  width: 10,
                ),
                // Title and date
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DiamondChangeType().get(bean.changeType),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      bean.createTime,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Right amount
          Text(
            bean.isIncreasing == 1
                ? "+${bean.changeNum}"
                : "-${bean.changeNum}",
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
