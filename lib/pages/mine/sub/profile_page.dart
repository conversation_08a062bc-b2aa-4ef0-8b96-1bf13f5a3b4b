import 'package:Chamatch/controller/user_controller.dart';
import 'package:Chamatch/im/media/rong_media.dart';
import 'package:Chamatch/manager/country_manager.dart';
import 'package:Chamatch/widget/theme_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<StatefulWidget> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  var controller = Get.find<UserController>();

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Profile",
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildListTile(
            context,
            title: 'Avatar',
            trailing: _buildCircleAvatar(
                controller.userBean.value?.headFileName ?? ""),
            onTap: () {
              _pickImage(context);
            },
          ),
          _buildListTile(
            context,
            title: 'Name',
            subtitle: '${controller.userBean.value?.nickName}',
            hasArrow: true,
            onTap: () {
              _editName(context);
            },
          ),
          _buildListTile(
            context,
            title: 'Date of birth',
            subtitle: '${controller.userBean.value?.birthday}',
            hasArrow: true,
            onTap: () {
              _pickDateOfBirth(context);
            },
          ),
          _buildListTile(
            context,
            title: 'Country',
            subtitle: '${controller.userBean.value?.country}',
            hasArrow: true,
            onTap: () {
              CountryManager.showCountryDialog(context, (item) {
                setState(() {
                  controller.userBean.value?.country = item?.countryCode ?? "";
                });
              });
            },
          ),
          Obx(() => _buildListTile(
                context,
                title: 'Gender',
                subtitle:
                    controller.userBean.value?.gender == 0 ? 'Male' : 'Female',
                hasArrow: true,
                onTap: () {
                  _pickGender(context);
                },
              )),
          const SizedBox(height: 16.0),
          ThemeButton(
              text: "Confirm",
              onTap: () {
                controller.updateProfile(controller.userBean.value);
              })
        ],
      ),
    );
  }

  Widget _buildListTile(BuildContext context,
      {required String title,
      String? subtitle,
      Widget? trailing,
      bool hasArrow = false,
      Function? onTap}) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: const TextStyle(color: Colors.white, fontSize: 16.0),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: const TextStyle(color: Colors.grey, fontSize: 14.0),
            )
          : null,
      trailing: trailing ??
          (hasArrow
              ? const Icon(Icons.chevron_right, color: Colors.grey)
              : null),
      onTap: () {
        onTap?.call();
      },
    );
  }

  Widget _buildCircleAvatar(String url) {
    return CircleAvatar(
      radius: 20.0,
      backgroundImage: NetworkImage(url),
    );
  }

  void _pickImage(BuildContext context) async {
    final picker = ImagePicker();
    final XFile? file = await picker.pickImage(source: ImageSource.gallery);
    if (file != null) {
      String? uploadResult = await controller.upload(file.path);
      // 上传图片到服务器
      if (uploadResult != null) {
        setState(() {
          controller.userBean.value?.headFileName = uploadResult;
        });
      }
    }
  }

  void _editName(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        TextEditingController nameController = TextEditingController(
          text: controller.userBean.value?.nickName,
        );
        return Stack(children: [
          Positioned.fill(
              child: ColoredBox(
            color: Colors.black, // 颜色
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8)),
              ),
              width: MediaQuery.of(context).size.width,
              height: 300,
            ),
          )),
          Image.asset("assets/bg_bottom_sheet.png"),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                          labelText: 'Enter your name',
                          labelStyle: TextStyle(color: Colors.white60)),
                    ),
                    const SizedBox(height: 20.0),
                    ThemeButton(
                        text: "Confirm",
                        onTap: () {
                          setState(() {
                            controller.userBean.value?.nickName =
                                nameController.text;
                          });
                        })
                  ],
                ),
              ))
        ]);
      },
    );
  }

  void _pickDateOfBirth(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1970),
      lastDate: DateTime.now(),
    ).then((selectedDate) {
      if (selectedDate != null) {
        setState(() {
          String formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate);
          controller.userBean.value?.birthday = formattedDate;
        });
      }
    });
  }

  void _pickGender(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Stack(
          children: [
            // 背景图片
            Positioned.fill(
                child: ColoredBox(
              color: Colors.black, // 颜色
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8)),
                ),
                width: MediaQuery.of(context).size.width,
                height: 200,
              ),
            )),
            Image.asset("assets/bg_bottom_sheet.png"),
            // 内容容器
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  const Text(
                    'Select Gender',
                    style: TextStyle(
                      color: Colors.white60,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // 单选选项
                  StatefulBuilder(
                    builder: (context, setState) {
                      // 获取当前性别值
                      int selectedGender =
                          controller.userBean.value?.gender ?? 0;
                      return Column(
                        children: [
                          RadioListTile<int>(
                            value: 0,
                            // 男性
                            groupValue: selectedGender,
                            onChanged: (value) {
                              setState(() {
                                selectedGender = value ?? 0;
                                controller.userBean.value?.gender =
                                    selectedGender; // 更新到 controller
                                controller.userBean.refresh();
                              });
                              Navigator.pop(context);
                            },
                            title: const Text(
                              'Male',
                              style: TextStyle(color: Colors.white),
                            ),
                            activeColor: Colors.green,
                          ),
                          RadioListTile<int>(
                            value: 1,
                            // 女性
                            groupValue: selectedGender,
                            onChanged: (value) {
                              setState(() {
                                selectedGender = value ?? 1;
                                controller.userBean.value?.gender =
                                    selectedGender;
                                controller.userBean.refresh();
                              });
                              Navigator.pop(context);
                            },
                            title: const Text(
                              'Female',
                              style: TextStyle(color: Colors.white),
                            ),
                            activeColor: Colors.green,
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  // ThemeButton(
                  //     text: "Confirm",
                  //     onTap: () {
                  //       controller.userBean.value?.gender = selectedGender;
                  //     })
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
