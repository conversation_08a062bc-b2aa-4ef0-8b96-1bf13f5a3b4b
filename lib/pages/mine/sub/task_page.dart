import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class TasksPage extends StatefulWidget {
  const TasksPage({super.key});

  @override
  State<TasksPage> createState() => _TasksPageState();
}

class _TasksPageState extends State<TasksPage> {
  // 示例状态：控制按钮的可用性
  bool isTaskClaimed = false;

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Reward Tasks",
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Career Tasks
            const Text(
              "Career Tasks",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 10),
            _buildTaskCard(
              "Cumulative Recharge",
              "Cumulative Recharge",
              "500",
              "Recharge",
              Color(0xff10DA23),
              onPressed: () {
                // Recharge button logic
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text("Recharge clicked")),
                );
              },
            ),
            const SizedBox(height: 10),
            _buildTaskCard(
              "Subscribe VIP",
              "Subscribe to be VIP membership",
              "500",
              "Go",
              Color(0xff10DA23),
              onPressed: () {
                // Go button logic
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text("Go clicked")),
                );
              },
            ),
            const SizedBox(height: 20),

            // Daily Tasks
            const Text(
              "Daily Tasks",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 10),
            _buildTaskCard(
              "10-Minute Call",
              "Call a girl for more than 10 minutes",
              "500",
              "Go",
              Color(0xff10DA23),
              onPressed: () {
                // Go button logic
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text("10-Minute Call clicked")),
                );
              },
            ),
            const SizedBox(height: 10),
            _buildTaskCard(
              "Match Girls",
              "Finish 10 matches with girls. Procedure: 10/10",
              "500",
              "Claim",
              Color(0xff10DA23),
              onPressed: () {
                setState(() {
                  isTaskClaimed = true;
                });
              },
            ),
            const SizedBox(height: 10),
            _buildTaskCard(
              "Send LIVE gift",
              "Send a gift to girls during video call",
              "500",
              isTaskClaimed ? "Claimed" : "Claim",
              isTaskClaimed ? Colors.grey : Color(0xff10DA23),
              onPressed: isTaskClaimed
                  ? null
                  : () {
                      setState(() {
                        isTaskClaimed = true;
                      });
                    },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskCard(String title, String subtitle, String points,
      String buttonText, Color buttonColor,
      {VoidCallback? onPressed}) {
    return Container(
      decoration: BoxDecoration(
        color:  Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 左侧图标和积分
          Column(
            children: [
              Image.asset(
                'assets/ic_diamond.png', // 替换为你的钻石图标路径
                height: 24,
                width: 24,
              ),
              const SizedBox(height: 8),
              Text(
                points,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          // 中间标题和描述
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // 右侧按钮
          // ElevatedButton(
          //   onPressed: onPressed,
          //   style: ElevatedButton.styleFrom(
          //     backgroundColor: buttonColor,
          //     shape: RoundedRectangleBorder(
          //       borderRadius: BorderRadius.circular(20),
          //     ),
          //   ),
          //   child: Text(
          //     buttonText,
          //     style: const TextStyle(
          //       color: Colors.white,
          //       fontWeight: FontWeight.bold,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
