import 'package:Chamatch/manager/payment_manager.dart';
import 'package:Chamatch/model/recharge_model.dart';
import 'package:Chamatch/pages/mine/sub/wallet/controllers/wallet_controller.dart';
import 'package:Chamatch/widget/item_payment_method_widget.dart';
import 'package:Chamatch/widget/item_recharge_widget.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';

class WalletView extends GetView<WalletController> {
  const WalletView({super.key});

  @override
  Widget build(BuildContext context) {
    controller.fetchRechargeList();
    return ThemeScaffold(
      title: "Recharge",
      body: Column(
        children: [
          const SizedBox(height: 30), // 顶部间距
          // 顶部展示区
          Center(
            child: Column(
              children: [
                Image.asset(
                  'assets/ic_diamond.png', // 大钻石图片
                  width: 100,
                  height: 100,
                ),
                Obx(() => Text(
                      "${controller.userBean.value?.diamond}",
                      style: const TextStyle(
                        fontSize: 24,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // 选择充值金额标题
          const Row(
            children: [
              SizedBox(
                width: 16,
              ),
              Text(
                'Please select the recharge amount',
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
          // 充值列表
          Expanded(
            child: Obx(() => ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: controller.rechargeList.length,
                  itemBuilder: (context, index) {
                    return ItemRechargeWidget(
                        bean: controller.rechargeList[index],
                        itemCallback: (item) => {
                              PaymentManager.showPaymentMethodDialog(
                                  context, item)
                            });
                  },
                )),
          ),
        ],
      ),
    );
  }
}
