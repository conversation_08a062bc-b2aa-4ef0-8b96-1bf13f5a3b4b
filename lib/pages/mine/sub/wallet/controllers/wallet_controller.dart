import 'package:Chamatch/http/http_request.dart';
import 'package:Chamatch/model/diamond_history_model.dart';
import 'package:Chamatch/model/order_model.dart';
import 'package:Chamatch/model/page_model.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/toast_util.dart';
import 'package:get/get.dart';
import 'package:Chamatch/model/recharge_model.dart';
import 'package:logger/logger.dart';

import '../../../../../repo/api_hub.dart';

class WalletController extends GetxController {
  final userBean = Get.find<UserInfoService>().user;
  final rechargeList = <RechargeBean>[].obs;
  final paymentList = <RechargeBean>[].obs;
  final diamondHistoryList = <DiamondHistoryBean>[].obs;

  void fetchRechargeList({String priceType = "1" /*1-充值，2-订阅，3-充值+订阅*/
      }) async {
    ToastUtil.loading();
    List<RechargeBean> result = await HttpRequest.request<RechargeBean>(
        APIHub.order.rechargeList,
        params: {"priceType": priceType},
        (p0) => RechargeBean.fromJson(p0));
    if (rechargeList.isNotEmpty) rechargeList.clear();
    rechargeList.addAll(result);
    ToastUtil.dismiss();
  }

  void fetchPaymentList(
      String itemName, String discountType, String country) async {
    ToastUtil.loading();
    List<RechargeBean> result = await HttpRequest.request<RechargeBean>(
        APIHub.order.paymentList,
        params: {
          'itemName': itemName,
          'discountType': discountType,
          'country': country,
        },
        (p0) => RechargeBean.fromJson(p0));
    if (paymentList.isNotEmpty) paymentList.clear();
    paymentList.addAll(result);
    ToastUtil.dismiss();
  }

  void createOrder(String priceId, String discountType,
      WorkerCallback<OrderBean> callback) async {
    Map<String, dynamic> params = {
      'priceId': priceId,
      'discountType': discountType,
    };
    try {
      HttpRequest.request<OrderBean>(
              APIHub.order.createOrder,
              method: Method.POST,
              params: params,
              (p0) => OrderBean.fromJson(p0))
          .then((onValue) => {callback(onValue)});
    } on Exception catch (e) {
      Logger().e('createOrder error: $e');
    }
  }

  fetchDiamondHistory(int current) async {
    Map<String, dynamic> params = {"current": current, "size": 20};
    try {
      PageBean<DiamondHistoryBean> pageBean = await HttpRequest.request(
          APIHub.user.diamondHistory,
          method: Method.POST,
          params: params,
          (p0) => PageBean<DiamondHistoryBean>.fromJson(
              p0, DiamondHistoryBean.fromJson));
      if (current == 1) {
        diamondHistoryList.clear();
        diamondHistoryList.assignAll(pageBean.records);
      } else {
        diamondHistoryList.addAll(pageBean.records);
      }
    } on Exception catch (e) {
      Logger().e('fetchDiamondHistory error: $e');
    }
  }
}
