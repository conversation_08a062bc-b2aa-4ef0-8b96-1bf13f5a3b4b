import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';

class PurchaseHistoryPage extends StatefulWidget {
  const PurchaseHistoryPage({super.key});

  @override
  State<StatefulWidget> createState() => _PurchaseHistoryPageState();
}

class _PurchaseHistoryPageState extends State<PurchaseHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Purchase History",
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: const [
            VIPSubscriptionItem(
              title: 'Week VIP',
              date: '2024-10-31',
              price: '\$12.99',
              status: SubscriptionStatus.success,
            ),
            SizedBox(height: 12),
            VIPSubscriptionItem(
              title: 'Month VIP',
              date: '2024-10-31',
              price: '\$12.99',
              status: SubscriptionStatus.unfinished,
            ),
            Sized<PERSON><PERSON>(height: 12),
            VIPSubscriptionItem(
              title: 'Season VIP',
              date: '2024-10-31',
              price: '\$12.99',
              status: SubscriptionStatus.unfinished,
            ),
          ],
        ),
      ),
    );
  }
}

enum SubscriptionStatus {
  success,
  unfinished,
}

class VIPSubscriptionItem extends StatelessWidget {
  final String title;
  final String date;
  final String price;
  final SubscriptionStatus status;

  const VIPSubscriptionItem({
    Key? key,
    required this.title,
    required this.date,
    required this.price,
    required this.status,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF242424),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {},
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Left dot and title section
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            date,
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Right price and status section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      price,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      status == SubscriptionStatus.success
                          ? 'Success'
                          : 'Unfinished',
                      style: TextStyle(
                        color: status == SubscriptionStatus.success
                            ? Colors.green
                            : Colors.red,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
