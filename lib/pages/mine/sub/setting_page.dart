import 'package:Chamatch/pages/login/views/login_view.dart';
import 'package:Chamatch/utils/Hive_util.dart';
import 'package:Chamatch/widget/CustomAlertDialog.dart';
import 'package:flutter/material.dart';
import 'package:Chamatch/pages/mine/sub/blocked_list_page.dart';
import 'package:Chamatch/pages/mine/sub/profile_page.dart';
import 'package:Chamatch/utils/jump.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<StatefulWidget> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  var checked = true;

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Setting",
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          _buildSwitchTile(context, title: 'Do not disturb', value: checked,
              onChanged: (value) {
            setState(() {
              checked = value;
            });
          }),
          // _buildListTile(
          //   context,
          //   title: 'Blocked list',
          //   hasArrow: true,
          //   onTap: () => Jump.to(context, const BlockedListPage()),
          // ),
          _buildListTile(
            context,
            title: 'Private Policy',
            hasArrow: true,
            onTap: () =>
                launchURL("https://www.chamatch.org/PrivacyPolicy.html"),
          ),
          _buildListTile(
            context,
            title: 'Terms of use',
            hasArrow: true,
            onTap: () =>
                {launchURL("https://www.chamatch.org/UseOfTerms.html")},
          ),
          _buildListTile(
            context,
            title: 'Version',
            subtitle: '1.0.0',
          ),
          _buildListTile(
            context,
            title: 'Delete account',
            hasArrow: true,
            onTap: () => {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return CustomAlertDialog(
                    title: "Remind",
                    content: "Are you sure delete your account?",
                    primaryButtonText: "Confirm",
                    onPrimaryButtonPressed: () {
                      HiveUtil.clear(HiveUtil.hiveBox);
                      Get.delete(); // 清除所有控制器
                      Get.offAllNamed("/login", predicate: (route) => false);
                    },
                    secondaryButtonText: "cancel",
                    onSecondaryButtonPressed: () {
                      Navigator.of(context).pop();
                    },
                  );
                },
              )
            },
          ),
          const SizedBox(height: 24.0),
          ElevatedButton.icon(
            onPressed: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return CustomAlertDialog(
                    title: "Remind",
                    content: "Are you sure you want to log out?",
                    primaryButtonText: "Confirm",
                    onPrimaryButtonPressed: () {
                      HiveUtil.clear(HiveUtil.hiveBox);
                      Get.delete(); // 清除所有控制器
                      Get.offAllNamed("/login", predicate: (route) => false);
                    },
                    secondaryButtonText: "cancel",
                    onSecondaryButtonPressed: () {
                      Navigator.of(context).pop();
                    },
                  );
                },
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.0),
              ),
              minimumSize: const Size(double.infinity, 48.0),
            ),
            icon: const Icon(Icons.logout, color: Colors.white),
            label: const Text('Logout',
                style: TextStyle(color: Colors.white, fontSize: 16.0)),
          ),
        ],
      ),
    );
  }

  void launchURL(String url) async {
    // if (await canLaunchUrl(Uri.http(url))) {
    await launchUrlString(url);
    // } else {
    //   throw 'Could not launch $url';
    // }
  }

  Widget _buildListTile(
    BuildContext context, {
    required String title,
    String? subtitle,
    bool hasArrow = false,
    VoidCallback? onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: const TextStyle(color: Colors.white, fontSize: 16.0),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: const TextStyle(color: Colors.grey, fontSize: 14.0),
            )
          : null,
      trailing:
          hasArrow ? const Icon(Icons.chevron_right, color: Colors.grey) : null,
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile.adaptive(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: const TextStyle(color: Colors.white, fontSize: 16.0),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.green,
    );
  }
}
