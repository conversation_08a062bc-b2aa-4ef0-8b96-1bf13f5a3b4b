import 'package:Chamatch/controller/user_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:Chamatch/widget/curved_level_progress.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:get/get.dart';

class LevelPage extends StatefulWidget {
  const LevelPage({super.key});

  @override
  State<StatefulWidget> createState() => _LevelPageState();
}

class _LevelPageState extends State<LevelPage> {
  var controller = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
      title: "Level",
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            children: [
              CurvedLevelProgress(),
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(color: Colors.yellow, width: 2.0),
                ),
                child: Row(
                  children: [
                    const CircleAvatar(
                      radius: 24.0,
                      backgroundColor: Colors.yellow,
                      child: Icon(Icons.star, color: Colors.white),
                    ),
                    const SizedBox(width: 12.0),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'LV ${controller.userBean.value.level}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${controller.userBean.value.nextGradeDiff} to next level',
                          style: TextStyle(color: Colors.grey, fontSize: 14.0),
                        ),
                      ],
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {},
                      child: const Text(
                        'Privilege',
                        style: TextStyle(color: Colors.yellow),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16.0),
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                alignment: Alignment.center,
                child: const Text(
                  'Lv0-10 Privilege',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 8.0),
              _buildPrivilegeTile(
                title: 'Recharge bonus',
                subtitle: '2%',
                hasButton: false,
                onTap: () {},
              ),
              _buildPrivilegeTile(
                title: 'Weekly bonus',
                buttonText: 'Get',
                hasButton: false,
                onTap: () {},
              ),
              _buildPrivilegeTile(
                title: 'Level identification',
                hasButton: false,
                onTap: () {},
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrivilegeTile({
    required String title,
    String? subtitle,
    String? buttonText,
    bool hasButton = false,
    VoidCallback? onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: const Icon(Icons.check_circle, color: Colors.green),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white, fontSize: 16.0),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: const TextStyle(color: Colors.grey, fontSize: 14.0),
            )
          : null,
      trailing: hasButton
          ? ElevatedButton(
              onPressed: onTap,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24.0),
                ),
              ),
              child: Text(
                buttonText ?? '',
                style: const TextStyle(color: Colors.white),
              ),
            )
          : null,
      onTap: onTap,
    );
  }
}
