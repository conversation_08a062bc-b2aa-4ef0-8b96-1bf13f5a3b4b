import 'package:Chamatch/pages/mine/sub/level_page.dart';
import 'package:Chamatch/pages/mine/sub/subscription_page.dart';
import 'package:Chamatch/pages/mine/sub/task_page.dart';
import 'package:Chamatch/pages/mine/sub/wallet/views/wallet_view.dart';
import 'package:Chamatch/utils/jump.dart';
import 'package:Chamatch/widget/theme_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../controllers/mine_controller.dart';
import '../sub/diamond_history_page.dart';
import '../sub/profile_page.dart';
import '../sub/setting_page.dart';

class MineView extends GetView<MineController> {
  const MineView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            Image.asset("assets/bg_mine.png",
                fit: BoxFit.cover, width: MediaQuery.of(context).size.width),
            Column(
              children: [
                // 顶部用户信息部分
                Container(
                  padding: const EdgeInsets.only(
                      top: 40, left: 16.0, right: 16, bottom: 0),
                  child: Column(
                    children: [
                      InkWell(
                          onTap: () => {Jump.to(context, const ProfilePage())},
                          child: Row(
                            children: [
                              Obx(() => CircleAvatar(
                                    radius: 40,
                                    backgroundImage: NetworkImage(controller
                                                .userInfoService
                                                .user
                                                .value
                                                ?.headFileName
                                                ?.isNotEmpty ==
                                            true
                                        ? controller.userInfoService.user.value
                                                ?.headFileName ??
                                            ""
                                        : "https://bdckj.s3.ap-southeast-1.amazonaws.com/1732872050043ic_default_avatar_male1.png"), // 用户头像
                                  )),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Obx(() => Text(
                                          controller.userInfoService.user.value
                                                  ?.nickName ??
                                              "",
                                          style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white),
                                        )),
                                    const SizedBox(height: 4),
                                    Text(
                                      'ID: ${controller.userInfoService.user.value?.userCode}',
                                      style:
                                          const TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                              const Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.white,
                                size: 25,
                              ),
                            ],
                          )),
                      const SizedBox(height: 24),
                      // 等级卡片
                      InkWell(
                          onTap: () {
                            Jump.to(context, const LevelPage());
                          },
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Image.asset("assets/bg_vip_card.png"),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(
                                        width: 22,
                                      ),
                                      Image.asset("assets/ic_vip_label.png",
                                          width: 50, height: 50),
                                      const SizedBox(width: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Obx(() => Text(
                                                'LV ${controller.userInfoService.user.value?.level ?? 0}',
                                                style: const TextStyle(
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.bold,
                                                  color: Color(0xffF2A840),
                                                ),
                                              )),
                                          Obx(() => Text(
                                                '${controller.userInfoService.user.value?.nextGradeDiff ?? 0} to next level',
                                                style: const TextStyle(
                                                  fontSize: 10,
                                                  color: Color(0xffF2A840),
                                                ),
                                              )),
                                          const SizedBox(height: 10),
                                          Container(
                                              width: 200,
                                              height: 4,
                                              child:
                                                  const LinearProgressIndicator(
                                                value: 0.6,
                                                // 进度，取值范围：0.0 到 1.0
                                                backgroundColor: Colors.grey,
                                                // 背景色
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                            Color>(
                                                        Color(
                                                            0xffF2A840)), // 进度条颜色
                                              )),
                                        ],
                                      )
                                    ],
                                  ),
                                ],
                              ),
                              const Positioned(
                                right: 20,
                                top: 20,
                                child: Row(
                                  children: [
                                    Text(
                                      'Privilege',
                                      style: TextStyle(color: Colors.orange),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: Colors.orange,
                                      size: 15,
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                    ],
                  ),
                ),

                // 按钮网格
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Obx(() => _buildCardButton(
                          assetsName: "assets/ic_diamond.png",
                          label: 'Coin',
                          value:
                              '${controller.userInfoService.user.value?.diamond}',
                          backgroundColor: Colors.white.withOpacity(0.1),
                          onTap: () {
                            Jump.toNamed("/wallet");
                          })),
                      const SizedBox(width: 16),
                      _buildCardButton(
                          assetsName: "assets/ic_v.png",
                          label: 'VIP',
                          value: 'Unactivated',
                          backgroundColor: Colors.white.withOpacity(0.1),
                          onTap: () {
//                             Jump.toNamed('/wallet');
                            Jump.to(context, const SubscriptionPage());
                          }),
                    ],
                  ),
                ),

                _buildMenuItem(
                    icon: Icons.card_giftcard,
                    iconColor: Colors.amber,
                    label: 'Reward task',
                    onTap: () {
                      Jump.to(context, const TasksPage());
                    }),

                const SizedBox(
                  height: 20,
                ),

                // 列表菜单
                Expanded(
                    child: ListView(
                  children: [
                    // _buildMenuItem(
                    //     icon: Icons.headset_mic,
                    //     label: 'Customer service',
                    //     onTap: () {
                    //       Jump.to(context, const SettingPage());
                    //     }),
                    _buildMenuItem(
                        icon: Icons.history,
                        label: 'Diamond History',
                        onTap: () {
                          Jump.to(context, const DiamondHistoryPage());
                        }),
                    _buildMenuItem(
                        icon: Icons.login,
                        label: 'Bind Google',
                        onTap: () {
                          Get.toNamed("/login");
                        }),
                    _buildMenuItem(
                        icon: Icons.settings,
                        label: 'Settings',
                        onTap: () {
                          Jump.to(context, const SettingPage());
                        }),
                  ],
                )),
              ],
            )
          ],
        ));
  }

  // 网格按钮
  Widget _buildCardButton({
    required String assetsName,
    required String label,
    required String value,
    required Color backgroundColor,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () {
          onTap.call();
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: backgroundColor,
          ),
          child: Row(
            children: [
              Image.asset(
                assetsName,
                width: 24,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  Text(
                    value,
                    style: const TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                ],
              ),
              const Spacer(),
              const Icon(Icons.arrow_forward_ios,
                  color: Colors.white, size: 15),
            ],
          ),
        ),
      ),
    );
  }

  // 菜单项
  Widget _buildMenuItem(
      {required IconData icon,
      Color iconColor = Colors.white,
      required String label,
      required Callback onTap}) {
    return Container(
      color: const Color(0xff1d1d1d),
      child: ListTile(
        leading: Icon(
          icon,
          color: iconColor,
          size: 24,
        ),
        title: Text(
          label,
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.grey,
          size: 15,
        ),
        onTap: () {
          onTap.call();
        },
      ),
    );
  }
}
