import 'package:Chamatch/utils/Hive_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../widget/theme_container.dart';
import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏透明
        statusBarIconBrightness: Brightness.light, // 状态栏图标为浅色
        systemNavigationBarColor: Colors.transparent, // 底部导航栏颜色
        systemNavigationBarIconBrightness: Brightness.light, // 导航栏图标浅色
      ),
    );
    return ThemeContainer(
        child: Stack(alignment: Alignment.bottomCenter, children: [
      Image(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        image: const AssetImage('assets/bg_login.png'),
      ),
      const Positioned(
          top: 250,
          left: 10,
          right: 10,
          child: Column(
            children: [
              Image(
                width: 88,
                height: 88,
                image: AssetImage('assets/ic_logo.png'),
              ),
              Text(
                "Chamatch",
                style: TextStyle(color: Colors.white),
              )
            ],
          )),
      Positioned(
          bottom: 50,
          left: 10,
          right: 10,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 30),
            decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
                borderRadius: const BorderRadius.all(Radius.circular(28.0))),
            child: Column(
              children: [
                _buildButton(context, "assets/ic_login_google.png",
                    "Sign In with Google", () {
                  controller.login(1);
                }),
                const SizedBox(height: 20),
                _buildButton(
                    context, "assets/ic_login_guest.png", "Sign in as guest",
                    () {
                  controller.login(5);
                }),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        controller.isAgree.value = !controller.isAgree.value;
                      },
                      child: Obx(() => Image(
                          width: 20,
                          height: 20,
                          image: AssetImage(controller.isAgree.value
                              ? "assets/ic_checkbox_active.png"
                              : "assets/ic_checkbox_normal.png"))),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text.rich(
                      TextSpan(
                        children: [
                          const TextSpan(
                            text: 'Agree ',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontFamily: 'Urbanist',
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          TextSpan(
                              text: 'Terms of Use',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontFamily: 'Urbanist',
                                fontWeight: FontWeight.w600,
                                decoration: TextDecoration.underline,
                                height: 0,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  launchUrlString(
                                      'https://www.chamatch.org/UseOfTerms.html');
                                }),
                          const TextSpan(
                            text: ' and ',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontFamily: 'Urbanist',
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          TextSpan(
                              text: 'Privacy Policies',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontFamily: 'Urbanist',
                                fontWeight: FontWeight.w600,
                                decoration: TextDecoration.underline,
                                height: 0,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  launchUrlString(
                                      'https://www.chamatch.org/PrivacyPolicy.html');
                                }),
                        ],
                      ),
                    )
                  ],
                )
              ],
            ),
          )),
      // Obx(EasyLoading.)
    ]));
  }

  _buildButton(
      BuildContext context, String assetImage, String text, Function onTap) {
    return InkWell(
        onTap: () {
          onTap.call();
        },
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: 44,
          padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 5),
          decoration: ShapeDecoration(
            color: Colors.transparent,
            shape: RoundedRectangleBorder(
              side: const BorderSide(width: 1, color: Colors.white),
              borderRadius: BorderRadius.circular(25),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image(width: 25, height: 25, image: AssetImage(assetImage)),
              const SizedBox(width: 5),
              Text(
                text,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: 'Urbanist',
                  fontWeight: FontWeight.w400,
                  height: 0,
                ),
              ),
            ],
          ),
        ));
  }
}
