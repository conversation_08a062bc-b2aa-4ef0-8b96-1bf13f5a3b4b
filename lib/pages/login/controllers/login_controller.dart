import 'package:Chamatch/manager/report_manager.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/toast_util.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';

import '../../../http/http_request.dart';
import '../../../model/user_model.dart';
import '../../../repo/api_hub.dart';
import '../../../storage/storage.dart';
import '../../../utils/Hive_util.dart';
import '../../../utils/platform_util.dart';

class LoginController extends GetxController {
  final GoogleSignIn _googleSignIn = GoogleSignIn(
      scopes: ['email'],
      clientId:
          "336264618152-igrd1r0nknvtg7upn9h570c58jms921s.apps.googleusercontent.com");
  final FirebaseAuth _auth = FirebaseAuth.instance;
  String? _referrerDetails = '';

  var isAgree = false.obs;

  Future<void> login(int type) async {
    if (!isAgree.value) {
      ToastUtil.error('Please read and agree to the relevant agreement first');
      return;
    }
    EasyLoading.show();
    GoogleSignInAuthentication? googleSignInAuthentication;
    if (type == 1) {
      googleSignInAuthentication = await _signInWithGoogle();
      if (googleSignInAuthentication == null) {
        ToastUtil.error("Google auth failure,please try again");
        EasyLoading.dismiss();
        return;
      }
    }
    await _initReferrerDetails();

    Map<String, dynamic> parameters = {
      'type': "$type",
      "authId": googleSignInAuthentication?.idToken ??
          HiveUtil.get<String>(StorageKeys.DEVICE_ID) ??
          "",
      "deviceId": HiveUtil.get<String>(StorageKeys.DEVICE_ID) ?? "",
      "userCategory": _referrerDetails,
      "platform": PlatformUtil.platformName()
    };
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.user.login,
        method: Method.POST,
        params: parameters,
        (p0) => UserBean.fromJson(p0),
        exception: (e) =>
            {debugPrint('parse exception $e'), EasyLoading.dismiss()});
    HiveUtil.save(StorageKeys.USER_KEY, result);
    Get.find<UserInfoService>().setUser(result);
    HiveUtil.save(StorageKeys.TOKEN_KEY, result.token);
    EasyLoading.dismiss();
    ReportManager.reportEvent("login", parameters: parameters);
    Get.offAndToNamed("/main-page");
  }

  Future<void> _initReferrerDetails() async {
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      ReferrerDetails referrerDetails =
          await AndroidPlayInstallReferrer.installReferrer;
      _referrerDetails = referrerDetails.installReferrer;
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<GoogleSignInAuthentication?> _signInWithGoogle() async {
    try {
      // 通过 Google 登录
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        // 用户取消登录
        return null;
      }

      // 获取 Google 登录的认证信息
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      return googleAuth;

      // 使用 Firebase 认证登录
      // final AuthCredential credential = GoogleAuthProvider.credential(
      //   accessToken: googleAuth.accessToken,
      //   idToken: googleAuth.idToken,
      // );
      //
      // // 使用 Firebase 认证进行登录
      // final UserCredential userCredential =
      //     await _auth.signInWithCredential(credential);
      // return userCredential.user;
    } catch (e) {
      print('Error during Google Sign-In: $e');
      return null;
    }
  }
}
