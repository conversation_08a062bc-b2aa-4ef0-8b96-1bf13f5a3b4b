import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../widget/CustomNetworkImage.dart';
import '../../../widget/GradientButton.dart';
import '../../../widget/LabelView.dart';
import '../controllers/profile_controller.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取底部安全区域高度
    final bottomPadding = MediaQuery.of(context).viewPadding.bottom;
    return Scaffold(
      body: Obx(() => Stack(
            children: [
              CustomScrollView(
                slivers: [
                  // 顶部大图
                  SliverAppBar(
                    expandedHeight: 300.0,
                    pinned: true,
                    backgroundColor: Colors.transparent,
                    flexibleSpace: FlexibleSpaceBar(
                      background: Stack(
                        children: [
                          CustomNetworkImage(
                              imageUrl:
                                  controller.userBean.value.headFileName ?? '',
                              width: double.infinity,
                              height: double.infinity,
                              borderRadius: 0 // 自定义圆角值
                              ),

                          /// 渐变蒙层
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                                height: 80,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      Colors.black.withOpacity(0.7),
                                    ],
                                  ),
                                ),
                                child: GestureDetector(
                                  onTap: () {
                                    controller.followUnfollow();
                                  },
                                  child: Align(
                                    alignment: Alignment.bottomRight,
                                    child: Container(
                                      width: 90,
                                      height: 34,
                                      margin: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: const Color(0X3313DB36),
                                        border: Border.all(
                                          color: const Color(0XFF11DB29),
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(controller.followed.value ? Icons.done : Icons.add,
                                                color: Colors.white, size: 10),
                                            Text(
                                              controller.followed.value ? 'unFollow' : 'Follow',
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                )),
                          ),
                        ],
                      ),
                    ),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        Get.back();
                      },
                    ),
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.more_horiz, color: Colors.white),
                        onPressed: () {
                          controller.showMoreOptions(context);
                        },
                      ),
                    ],
                  ),
                  // 主内容
                  SliverToBoxAdapter(
                      child: Container(
                    color: Colors.black,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 姓名、年龄和国家
                          Row(
                            children: [
                              ConstrainedBox(
                                  constraints:
                                      const BoxConstraints(maxWidth: 250),
                                  child: Text(
                                    '${controller.userBean.value.nickName}, ${controller.userBean.value.age}',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )),
                              const SizedBox(width: 8),
                              Text(
                                controller.userBean.value.onlineStatus == '1'
                                    ? 'online'
                                    : 'offline',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Row(
                            children: [
                              Icon(
                                Icons.flag,
                                color: Colors.green,
                                size: 14,
                              ),
                              SizedBox(width: 4),
                              Text('Thailand | '),
                              Text('Followers: 1.5k'),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: controller.labelList
                                .map((label) => LabelView(
                                      label: label.labelName ?? '',
                                      value: label.likeCount ?? 0,
                                    ))
                                .toList(),
//                            children: [
//                              LabelView(label: 'Art', value: 1),
//                            ],
                          ),
                          const SizedBox(height: 20),
                          _contentTitle('photos'),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: controller.imageList.isEmpty ? 20 : 150,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 10.0),
                              // 添加右边距
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: controller.imageList.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0), // 添加图片间距
                                    child: CustomNetworkImage(
                                      imageUrl:
                                          controller.imageList[index].fileUrl,
                                      width: 150,
                                      height: 150,
                                      borderRadius: 8.0, // 自定义圆角值
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          _contentTitle('Videos'),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: controller.videoList.isEmpty ? 20 : 150,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 10.0),
                              // 添加右边距
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: controller.videoList.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0), // 添加图片间距
                                    child: CustomNetworkImage(
                                      imageUrl:
                                          controller.videoList[index].thumbnail,
                                      width: 150,
                                      height: 150,
                                      borderRadius: 8.0,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),
                          _contentTitle('Gifts'),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: 100,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: controller.giftList.length,
                              itemBuilder: (context, index) {
                                final gift = controller.giftList[index];
                                return Container(
                                  width: 80,
                                  height: 80,
                                  margin: const EdgeInsets.only(right: 8),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: const Color(0xFFCE9E52)),
                                    color: const Color(0x1AE9BE79),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.network(
                                        gift.giftIcon ?? '',
                                        width: 34,
                                        height: 34,
                                        fit: BoxFit.fill,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'X${gift.num}',
                                        style: const TextStyle(fontSize: 12),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),

                          SizedBox(
                            height: 80 + bottomPadding,
                          ),
                        ],
                      ),
                    ),
                  )),
                ],
              ),
              // 底部按钮
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.fromLTRB(
                    16.0,
                    8.0,
                    16.0,
                    8.0 + bottomPadding, // 底部添加安全区域高度的内边距
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _gradientButton(
                        onPressed: () {
                          controller.onChat();
                        },
                        title: 'Chat',
                        colors: const [Color(0xfff9db1b), Color(0xfff46b01)],
                      ),
                      GradientButton(
                        text: "Video",
                        onPressed: () {
                          controller.onVideo();
                        },
                        width: 200,
                        // 可选参数
                        height: 50,
                        // 可选参数
                        borderRadius: 30.0, // 可选参数
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )),
    );
  }

  Widget _contentTitle(String title) {
    return Row(
      children: [
        Container(
          width: 3, // 设置宽度为 3
          height: 16, // 设置高度为 6
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter, // 渐变从上开始
              end: Alignment.bottomCenter, // 渐变到下结束
              colors: [
                Color(0xFF10DB21), // 渐变起始颜色
                Color(0xFF23E0BA), // 渐变结束颜色
              ],
            ),
          ),
        ),
        const SizedBox(width: 11),
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        )
      ],
    );
  }

  // 公共按钮组件
  Widget _gradientButton({
    required VoidCallback onPressed,
    required String title,
    required List<Color> colors,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(20),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Text(title, style: const TextStyle(color: Colors.white)),
      ),
    );
  }
}
