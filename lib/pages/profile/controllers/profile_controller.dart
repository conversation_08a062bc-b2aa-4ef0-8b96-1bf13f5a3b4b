import 'dart:io';

import 'package:Chamatch/model/anchor_Label_model.dart';
import 'package:Chamatch/model/anchor_file_model.dart';
import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../http/http_request.dart';
import '../../../manager/payment_manager.dart';
import '../../../model/gift_model.dart';
import '../../../repo/api_hub.dart';
import '../../../utils/DeviceInfo.dart';
import '../../../utils/constants.dart';

class ProfileController extends GetxController {
  final userBean = UserBean.empty().obs;
  final giftList = <GiftBean>[].obs;
  final imageList = <AnchorFileBean>[].obs;
  final videoList = <AnchorFileBean>[].obs;
  final labelList = <AnchorLabelBean>[].obs;

  final fileTypeImage = '1';
  final fileTypeVideo = '2';

  final followed = false.obs;

  final userInfoService = Get.find<UserInfoService>();

  @override
  void onInit() {
    super.onInit();
    UserBean argUserBean = Get.arguments;
    userBean.value = argUserBean;
    if (argUserBean.id != null) {
      getPersonInfo(argUserBean.id!);
      getUserGift(argUserBean.id!);
      getFollowFlag(argUserBean.id!);
    }
  }

  Future<void> getPersonInfo(String anchorId) async {
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.anchor.anchorDetail,
        method: Method.GET,
        params: {'id': anchorId},
        (p0) => UserBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});

    if (result.anchorFileList != null) {
      List<AnchorFileBean> tmpImageList = [];
      List<AnchorFileBean> tmpVideoList = [];
      for (var file in result.anchorFileList!) {
        if (file.fileType == fileTypeImage) {
          tmpImageList.add(file);
        } else if (file.fileType == fileTypeVideo) {
          tmpVideoList.add(file);
        }
      }
      imageList.assignAll(tmpImageList);
      videoList.assignAll(tmpVideoList);
      labelList.assignAll(result.anchorLabelList ?? []);
    }
    userBean.value = result;
  }

  Future<void> getFollowFlag(String anchorId) async {
    String result = await HttpRequest.request<String>(
        APIHub.anchor.followFlag,
        method: Method.POST,
        queryData: {'id': anchorId},
        (p0) => p0,
        exception: (e) => {debugPrint('parse exception $e')});
    followed.value = result == '1';
  }

  Future<void> getUserGift(String anchorId) async {
    List<GiftBean> result = await HttpRequest.request<GiftBean>(
        APIHub.user.userGift,
        method: Method.GET,
        params: {'anchorId': anchorId},
        (p0) => GiftBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});

    giftList.assignAll(result);
  }

  void onChat() {
    Get.toNamed('/chat', arguments: userBean.value);
  }

  void onVideo() {
    int userDiamond = userInfoService.user.value?.diamond ?? 0;
    if (userDiamond < userBean.value.videoPrice) {
      if (Get.context != null) {
        PaymentManager.showRechargeDialog(Get.context!);
      }
    } else {
      Get.toNamed('/video-chat', arguments: [
        userBean.value,
        false,
        false,
        Constants.videoSourceNormal
      ]);
    }
  }

  void showMoreOptions(BuildContext context) {
    // 显示更多选项弹窗
    // 获取底部安全区域的高度
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Color(0XFF2b2b2b) ,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        padding: EdgeInsets.fromLTRB(
          16,
          16,
          16,
          16 + bottomPadding, // 底部添加安全区域高度的内边距
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              // leading: const Icon(Icons.flag, color: Colors.red),
              title: const Text('Report', style: TextStyle(color: Colors.white)),
              onTap: () {
                Get.back(); // 关闭弹窗
                print('Report selected');
              },
            ),
            // ListTile(
            //   leading: const Icon(Icons.block, color: Colors.black),
            //   title: const Text('Block'),
            //   onTap: () {
            //     Get.back(); // 关闭弹窗
            //     print('Block selected');
            //   },
            // ),
            ListTile(
              title: const Center(child: Text('Cancel', style: TextStyle(color: Colors.white60),)),
              onTap: () => Get.back(), // 关闭弹窗
            ),
          ],
        ),
      ),
    );
  }

  Future<void> follow(String anchorId) async {
    await HttpRequest.request<String>(
        APIHub.anchor.follow,
        method: Method.POST,
        queryData: {'id': anchorId, 'role': "1"},
        (p0) => p0,
        exception: (e) => {debugPrint('parse exception $e')});
  }

  Future<void> unFollow(String anchorId) async {
    await HttpRequest.request<dynamic>(
        APIHub.anchor.unFollow,
        method: Method.POST,
        queryData: {'id': anchorId},
        (p0) => p0,
        exception: (e) => {debugPrint('parse exception $e')});
  }

  String getFileUrl(String type, int index) {
    if (type == fileTypeImage) {
      return imageList[index].fileName;
    } else if (type == fileTypeVideo) {
      return videoList[index].thumbnail;
    }
    return '';
  }

  void followUnfollow() {
    if (followed.value) {
      followed.value = false;
      if (userBean.value.id != null) {
        unFollow(userBean.value.id!);
      }
    } else {
      followed.value = true;
      if (userBean.value.id != null) {
        follow(userBean.value.id!);
      }
    }
  }
}
