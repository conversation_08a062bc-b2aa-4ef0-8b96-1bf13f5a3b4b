import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../../widget/CustomNetworkImage.dart';
import '../../../widget/refresh.dart';
import '../controllers/conversation_list_controller.dart';

class ConversationListView extends GetView<ConversationListController> {
  const ConversationListView({super.key});

  @override
  Widget build(BuildContext context) {
    RefreshController refreshController =
        RefreshController(initialRefresh: true);

    return Scaffold(
      body: Refresh(
        child: Obx(() => ListView.builder(
              itemCount: controller.messageList.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    _buildPostItem(context, controller.messageList[index]),
                    const Divider(
                      color: Color(0x1AFFFFFF), // 横线分割
                      height: 0.5,
                    ),
                  ],
                );
              },
            )),
        onRefresh: () {
          controller.getConversationList(0);
        },
        onLoad: () {
          controller.getConversationList(controller.getConversationLastTime());
        },
      ),
    );
  }

  void _showCustomDialog(BuildContext context, RCIMIWConversation message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              message.top ?? false
                  ? SizedBox(
                      height: 40,
                      child: ListTile(
                        leading: const Icon(Icons.arrow_downward),
                        title: const Text("取消置顶"),
                        onTap: () {
                          controller.changeConversationTopStatus(message);
                          Navigator.pop(context); // 关闭弹窗
                        },
                      ))
                  : SizedBox(
                      height: 40,
                      child: ListTile(
                        leading: const Icon(Icons.arrow_upward),
                        title: const Text("置顶该聊天"),
                        onTap: () {
                          controller.changeConversationTopStatus(message);
                          Navigator.pop(context); // 关闭弹窗
                        },
                      )),
              const Divider(),
              SizedBox(
                  height: 40,
                  child: ListTile(
                    leading: const Icon(Icons.delete),
                    title: const Text("删除该聊天"),
                    onTap: () {
                      Navigator.pop(context);
                      controller.removeConversation(message);
                    },
                  )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostItem(BuildContext context, RCIMIWConversation message) {
    return Material(
      color: Colors.transparent, // 设置透明背景色，防止遮挡
      child: InkWell(
          onTap: () {
            controller.gotoConversation(message); // 延迟调用
          },
          onLongPress: () {
            if (!controller.isSystemOrCustomService(message.targetId)) {
              _showCustomDialog(context, message);
            }
          },
          child: Container(
              color: message.top ?? false
                  ? const Color(0xFF202020)
                  : Colors.transparent,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                child: Row(
                  children: [
                    // 头像
                    CustomHeaderImage(
                      imageUrl:
                          controller.getHeader(message.lastMessage?.extra),
                      width: 50,
                    ),
                    const SizedBox(width: 12),
                    // 用户名和最后一条消息标题
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.getNickName(message),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            controller.getConversationItemLastMsg(message),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0x7FFFFFFF),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    // 消息时间和未读消息数
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          message.lastMessage?.sentTime == null
                              ? ''
                              : controller.formatTimestamp(
                                  message.lastMessage!.sentTime!),
                          style: const TextStyle(
                            fontSize: 11,
                            color: Color(0x7FFFFFFF),
                          ),
                        ),
                        (message.unreadCount == null ||
                                (message.unreadCount ?? 0) <= 0)
                            ? Container()
                            : Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text("${message.unreadCount}",
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 12)),
                              ),
                      ],
                    ),
                  ],
                ),
              ))),
    );
  }
}
