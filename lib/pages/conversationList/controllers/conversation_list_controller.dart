import 'dart:convert';

import 'package:Chamatch/im/custom_message/mikchat_ask_gift_message.dart';
import 'package:Chamatch/im/custom_message/mikchat_gift_message.dart';
import 'package:Chamatch/im/custom_message/mikchat_message.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../../engine/event_bus.dart';
import '../../../im/im_engine.dart';
import '../../../model/user_model.dart';
import '../../../utils/constants.dart';
import '../../../utils/log.dart';

class ConversationListController extends GetxController {
  var messageList = <RCIMIWConversation>[].obs;

  @override
  void onInit() {
    super.onInit();
    bus.on(EventBus.eventRefreshConversation, _getConversation);
    bus.on(EventBus.eventReceiveMessage, _refreshConversation);
    getConversationList(0);
  }

  @override
  void onClose() {
    super.onClose();
    bus.off(EventBus.eventRefreshConversation);
    bus.off(EventBus.eventReceiveMessage);
  }

  int getConversationLastTime() {
    if (messageList.isNotEmpty) {
      var endTime = messageList[messageList.length - 1].operationTime;
      return endTime ?? 0;
    }
    return 0;
  }

  String getConversationItemLastMsg(RCIMIWConversation conversation) {
    if (conversation.lastMessage is RCIMIWTextMessage) {
      RCIMIWTextMessage textMessage =
          conversation.lastMessage as RCIMIWTextMessage;
      return textMessage.text ?? '';
    } else if (conversation.lastMessage is RCIMIWImageMessage) {
      return '[图片]';
    } else if (conversation.lastMessage is RCIMIWVoiceMessage) {
      return '[语音]';
    }else if (conversation.lastMessage is MikChatGiftMessage || conversation.lastMessage is MikChatAskGiftMessage) {
      return '[礼物]';
    }
    return '';
  }

  Future<void> getConversationList(int startTime) async {
    List<RCIMIWConversationType> conversationTypesInt = [];
    conversationTypesInt.add(RCIMIWConversationType.private);
    IRCIMIWGetConversationsCallback callback = IRCIMIWGetConversationsCallback(
        onSuccess: (List<RCIMIWConversation>? t) {
      if (startTime == 0) {
        messageList.clear();
      }
      if (t != null) {
        if (startTime == 0) {
          messageList.assignAll(t);
        } else {
          messageList.addAll(t);
        }
        LogUtil.e("RongYun getConversation suc ${messageList.length}");
        messageList.removeWhere((item)=> item.targetId == Constants.rongYunCustomServiceId || item.targetId == Constants.rongYunSystemId);
        _sorMessageList();
      }
    }, onError: (int? code) {
      LogUtil.e("RongYun getConversation exception: $code");
    });

    int? code = await IMEngineManager().engine?.getConversations(
        conversationTypesInt, null, startTime, 50,
        callback: callback);
    if (code != 0) {
      LogUtil.e("RongYun getConversation failed: $code");
    } else {
      LogUtil.e("RongYun getConversation suc");
    }
  }

  _sorMessageList() {
    // 排序：先按 isTop 排序，再按 sentTime 排序
    messageList.sort((a, b) {
      // Step 1: 特殊的会话ID排序：先排序 id 为 8888 的，接着是 6666 的
      if (a.targetId == Constants.rongYunSystemId) return -1; // 8888 排在最前面
      if (b.targetId == Constants.rongYunSystemId) return 1;
      if (a.targetId == Constants.rongYunCustomServiceId)
        return -1; // 6666 排在第二位
      if (b.targetId == Constants.rongYunCustomServiceId) return 1;
      // 先比较 isTop 字段，isTop 为 true 的排在前面
      if (a.top != b.top) {
        return a.top ?? false ? -1 : 1;
      }
      // 如果 isTop 相同，按 sentTime 排序，越新的时间排前面
      return b.operationTime?.compareTo(a.operationTime ?? 0) ?? -1;
    });
  }

  _refreshConversation(arg) async {
    RCIMIWMessage result = arg as RCIMIWMessage;
    if (result.targetId == null) {
      return;
    }
    LogUtil.e("vvvvvvvvvv ${result.targetId}");
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = result.targetId!;
    IRCIMIWGetConversationCallback? callback =
        IRCIMIWGetConversationCallback(onSuccess: (RCIMIWConversation? t) {
      if (t != null) {
        int index = messageList
            .indexWhere((conversation) => conversation.targetId == t.targetId);
        if (index != -1) {
          // 替换掉目标消息
          messageList[index] = t;
        } else {
          //没有消息插入到第一条
          messageList.insert(0, t);
          messageList.refresh();
        }
        _sorMessageList();
      }
    }, onError: (int? code) {
      LogUtil.e("_getConversation error $code");
    });

    int? code = await IMEngineManager()
        .engine
        ?.getConversation(type, targetId, null, callback: callback);
  }

  _getConversation(arg) async {
    Map result = arg as Map;
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = arg['targetId'];
    IRCIMIWGetConversationCallback? callback =
        IRCIMIWGetConversationCallback(onSuccess: (RCIMIWConversation? t) {
      if (t != null) {
        int index = messageList
            .indexWhere((conversation) => conversation.targetId == t.targetId);
        if (index != -1) {
          // 替换掉目标消息
          messageList[index] = t;
        } else {
          messageList.insert(0, t);
          messageList.refresh();
        }
        _sorMessageList();
      }
    }, onError: (int? code) {
      LogUtil.e("_getConversation error $code");
    });

    int? code = await IMEngineManager()
        .engine
        ?.getConversation(type, targetId, null, callback: callback);
  }

  String formatTimestamp(int timestamp) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    if (dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day) {
      return "${dateTime.hour}:${dateTime.minute}";
    } else if (dateTime.year == now.year) {
      return "${dateTime.month}-${dateTime.day}";
    } else {
      return "${dateTime.year}-${dateTime.month}-${dateTime.day}";
    }
  }

  bool isSystemOrCustomService(String? targetId) {
    return targetId == Constants.rongYunCustomServiceId ||
        targetId == Constants.rongYunSystemId;
  }

  getHeader(String? messageExtra) {
    String headFileName = '';
    if (messageExtra != null) {
      jsonDecode(messageExtra).forEach((key, value) {
        if (key == 'headFileName') {
          headFileName = value;
        }
      });
    }
    return headFileName;
  }

  getNickName(RCIMIWConversation message) {
    String nickName = '';
    if (message.lastMessage?.extra != null) {
      jsonDecode(message.lastMessage!.extra!).forEach((key, value) {
        if (key == 'nickName') {
          nickName = value;
          return nickName;
        }
      });
    }else {
      return message.targetId;
    }
    return nickName;
  }

  gotoConversation(RCIMIWConversation clickConversation) {
    UserBean userBean = UserBean.empty();
    LogUtil.e("vvvvvvv ${clickConversation.lastMessage?.userInfo?.name}");
    userBean.nickName = clickConversation.lastMessage?.userInfo?.name ?? '';
    userBean.headFileName =
        clickConversation.lastMessage?.userInfo?.portrait ?? '';
    userBean.id = clickConversation.targetId ?? '';
    Get.toNamed('/chat', arguments: userBean);
  }

  changeConversationTopStatus(RCIMIWConversation message) async {
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = message.targetId ?? '';
    bool top = message.top ?? false;
    IRCIMIWChangeConversationTopStatusCallback? callback =
        IRCIMIWChangeConversationTopStatusCallback(
            onConversationTopStatusChanged: (int? code) {
      if (code == 0) {
        message.top = !top;
        int index = messageList.indexWhere(
            (conversation) => conversation.targetId == message.targetId);
        if (index != -1) {
          messageList[index] = message;
          _sorMessageList();
        }
      } else {
        EasyLoading.showToast("操作失败");
      }
    });

    int? code = await IMEngineManager().engine?.changeConversationTopStatus(
        type, targetId, null, !top,
        callback: callback);
  }

  removeConversation(RCIMIWConversation message) async {
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = message.targetId ?? '';
    IRCIMIWRemoveConversationCallback? callback =
        IRCIMIWRemoveConversationCallback(onConversationRemoved: (int? code) {
      if (code == 0) {
        int index = messageList.indexWhere(
                (conversation) => conversation.targetId == message.targetId);
        if (index != -1) {
          messageList.removeAt(index);
        }
      } else {
        EasyLoading.showToast("删除失败");
      }
    });
    int? code = await IMEngineManager()
        .engine
        ?.removeConversation(type, targetId, null, callback: callback);
  }
}
