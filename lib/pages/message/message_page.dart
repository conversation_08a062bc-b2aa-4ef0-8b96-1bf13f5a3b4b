import 'package:flutter/material.dart';

import '../../widget/theme_scaffold.dart';
import '../callHistory/views/call_history_view.dart';
import '../conversationList/views/conversation_list_view.dart';

class MessagePage extends StatefulWidget {
  const MessagePage({super.key});

  @override
  State<StatefulWidget> createState() => _MessagePageState();
}

class _MessagePageState extends State<MessagePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose(); // Dispose the TabController
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
        title: "Video chat",
        appBarBottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Message'),
            Tab(text: 'Calls'),
          ],
        ),
        body: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
          controller: _tabController,
          children: const [
            ConversationL<PERSON><PERSON>ie<PERSON>(),
            CallHistoryView(),
          ],
        ));
  }
}
