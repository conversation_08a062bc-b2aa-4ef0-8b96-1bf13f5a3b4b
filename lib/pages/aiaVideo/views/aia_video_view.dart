import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../../../widget/CustomNetworkImage.dart';
import '../controllers/aia_video_controller.dart';

class AiaVideoView extends GetView<AiaVideoController> {
  const AiaVideoView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
      ),
    );
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(() {
        return Stack(
          children: [
            if (controller.isPlaying.value) ...[
              Center(
                  child: SizedBox.expand(
                      child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                    height: 16,
                    width: 9,
                    child: VideoPlayer(controller.videoController)),
              ))),
              <PERSON><PERSON>(
                  alignment: Alignment.topRight,
                  child: Padding(
                      padding: const EdgeInsets.only(top: 160, right: 10),
                      child: SizedBox(
                          width: 90,
                          height: 120,
                          child: AgoraVideoView(
                            controller: VideoViewController(
                              rtcEngine: controller.engine,
                              canvas: const VideoCanvas(uid: 0),
                              useFlutterTexture: controller.isUseFlutterTexture,
                              useAndroidSurfaceView:
                                  controller.isUseAndroidSurfaceView,
                            ),
                            onAgoraVideoViewCreated: (viewId) {
                              controller.engine.startPreview();
                            },
                          ))))
            ],
            if (!controller.isPlaying.value) ...[
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center, // 垂直方向居中
                  crossAxisAlignment: CrossAxisAlignment.center, // 水平方向居中
                  children: [
                    if (controller.isListenedClicked.value)
                      const Text(
                        'Connecting...',
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    const SizedBox(height: 16),
                    Stack(
                      children: [
                        Column(
                          children: [
                            CustomNetworkImage(
                                imageUrl: controller.anchorBigImage.value,
                                width: MediaQuery.of(context).size.width - 20,
                                height: 520,
                                borderRadius: 12),
                            const SizedBox(
                              height: 35,
                            )
                          ],
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Stack(
                            children: [
                              Image.asset(
                                'assets/bg_call_center_diamond.png',
                                width: 84,
                                height: 22,
                              ),
                              SizedBox(
                                  width: 84,
                                  height: 24,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'assets/ic_diamond.png',
                                        width: 20,
                                        height: 14,
                                      ),
                                      Text(
                                          '${controller.anchorInfo.value.videoPrice.toString()}/min',
                                          style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white))
                                    ],
                                  ))
                            ],
                          ),
                        ),
                        Positioned(
                            bottom: 0,
                            left: (!controller.isListenedClicked.value)
                                ? 58
                                : MediaQuery.of(context).size.width / 2 - 54,
                            child: GestureDetector(
                              onTap: () {
                                controller.showCloseDialog(context);
                              },
                              child: Image.asset(
                                'assets/ic_call_cancel.png',
                                width: 70,
                                height: 70,
                              ),
                            )),
                        if (!controller.isListenedClicked.value)
                          Positioned(
                            bottom: 0,
                            right: 58,
                            child: GestureDetector(
                                onTap: () {
                                  controller.listenCall();
                                },
                                child: Image.asset(
                                  'assets/ic_call_listen.png',
                                  width: 70,
                                  height: 70,
                                )),
                          )
                      ],
                    )
                  ],
                ),
              ),
            ],
            Positioned(
                top: 60,
                left: 10,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0x4D000000),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 5),
                        child: Row(
                          children: [
                            CustomHeaderImage(
                                imageUrl:
                                    controller.anchorInfo.value.headFileName ??
                                        '',
                                width: 35),
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(controller.anchorInfo.value.nickName ?? '',
                                    style: const TextStyle(
                                        fontSize: 16, color: Colors.white)),
                                Text(
                                    '${controller.anchorInfo.value.country},${controller.anchorInfo.value.age}',
                                    style: const TextStyle(
                                        fontSize: 12, color: Colors.white))
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    if(controller.isPlaying.value)
                    Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: Text(
                        controller.timeText.value,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 20),
                      ),
                    )
                  ],
                )),
            Positioned(
                top: 60,
                right: 10,
                child: SizedBox(
                  height: 27,
                  child: !controller.isPlaying.value
                      ? Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFF101010),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset('assets/ic_diamond.png'),
                                Text(
                                    '${controller.anchorInfo.value.videoPrice.toString()}/min',
                                    style: const TextStyle(
                                        fontSize: 12, color: Colors.white))
                              ],
                            ),
                          ))
                      : Row(
                          children: [
                            // GestureDetector(
                            //     onTap: () {
                            //       controller.showReportDialog();
                            //     },
                            //     child: Image.asset(
                            //       'assets/ic_call_report.png',
                            //       width: 20,
                            //       height: 20,
                            //     )),
                            const SizedBox(
                              width: 20,
                            ),
                            GestureDetector(
                                onTap: () {
                                  controller.showCloseDialog(context);
                                },
                                child: Image.asset(
                                  'assets/ic_call_close.png',
                                  width: 20,
                                  height: 20,
                                )),
                          ],
                        ),
                )),
          ],
        );
      }),
    );
  }
}
