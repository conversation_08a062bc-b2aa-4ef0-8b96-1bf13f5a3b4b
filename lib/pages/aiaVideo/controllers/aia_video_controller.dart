import 'dart:io';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../../../http/http_request.dart';
import '../../../manager/IncomingAnchorManager.dart';
import '../../../manager/payment_manager.dart';
import '../../../model/user_model.dart';
import '../../../repo/api_hub.dart';
import '../../../utils/log.dart';
import '../../../widget/CustomAlertDialog.dart';

class AiaVideoController extends GetxController {
  final isPlaying = false.obs;
  late VideoPlayerController videoController;
  RxBool isInitialized = false.obs;

  final anchorInfo = UserBean.empty().obs;
  final anchorBigImage = ''.obs;
  late final RtcEngine engine;
  late final RtcEngineEventHandler _rtcEngineEventHandler;
  bool isUseFlutterTexture = false;
  bool isUseAndroidSurfaceView = false;
  final isListenedClicked = false.obs;
  final manager = IncomingAnchorManager();
  final timeText = ''.obs;
  bool isPlayFinish = false;

  @override
  void onInit() {
    super.onInit();
    UserBean argUserBean = Get.arguments[0];
    anchorInfo.value = argUserBean;
    _setAnchorBigImg();

    if (anchorInfo.value.id != null) {
      _getAnchorLitDetail(anchorInfo.value.id!);
      _initEngine();
    }
    String videoPath = Get.arguments[1] as String; // 获取视频路径
    videoController = VideoPlayerController.file(File(videoPath))
      ..addListener(() {
        timeText.value = formatDuration(videoController.value.position);
        if (videoController.value.position >= videoController.value.duration &&
            !videoController.value.isPlaying) {
          // 视频播放完毕的回调
          print("Video finished playing");
          if (!isPlayFinish) {
            isPlayFinish = true;
            _deleteFile(videoPath);
            _onVideoFinished();
          }
        }
      })
      ..initialize().then((_) {
        isInitialized.value = true;
      });

    manager.pauseLoop();
  }

  @override
  onClose() async {
    videoController.removeListener(() {});
    videoController.dispose();
    engine.unregisterEventHandler(_rtcEngineEventHandler);
    await engine.leaveChannel();
    await engine.release();
    super.onClose();
  }

  String formatDuration(Duration duration) {
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes % 60;
    final int seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return "${hours.toString().padLeft(2, '0')}:"
          "${minutes.toString().padLeft(2, '0')}:"
          "${seconds.toString().padLeft(2, '0')}";
    } else {
      return "${minutes.toString().padLeft(2, '0')}:"
          "${seconds.toString().padLeft(2, '0')}";
    }
  }

  _onVideoFinished() {
    if (Get.context != null) {
      PaymentManager.showRechargeDialog(Get.context!);
    }
  }

  _setAnchorBigImg() {
    if (anchorInfo.value.photos != null &&
        anchorInfo.value.photos!.isNotEmpty) {
      anchorBigImage.value = anchorInfo.value.photos![0];
    } else {
      anchorBigImage.value = anchorInfo.value.headFileName ?? '';
    }
  }

  Future<void> _getAnchorLitDetail(String anchorId) async {
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.anchor.anchorlitDetail,
        method: Method.GET,
        params: {'id': anchorId},
        (p0) => UserBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});

    anchorInfo.value = result;
    _setAnchorBigImg();
  }

  Future<void> _initEngine() async {
    engine = createAgoraRtcEngine();
    await engine.initialize(const RtcEngineContext(
      appId: '9bebac55156e49a7ada7616f1ea20399',
    ));
    _rtcEngineEventHandler = RtcEngineEventHandler(
      onError: (ErrorCodeType err, String msg) {
        LogUtil.e('[onError] err: $err, msg: $msg');
        EasyLoading.showToast('The other party has hung up');
      },
      onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
        LogUtil.w(
            '[onJoinChannelSuccess] connection: ${connection.toJson()} elapsed: $elapsed');
      },
      onUserJoined: (RtcConnection connection, int rUid, int elapsed) {
        LogUtil.w(
            '[onUserJoined] connection: ${connection.toJson()} remoteUid: $rUid elapsed: $elapsed');
      },
      onUserOffline:
          (RtcConnection connection, int rUid, UserOfflineReasonType reason) {
        LogUtil.w(
            '[onUserOffline] connection: ${connection.toJson()}  rUid: $rUid reason: $reason');
        EasyLoading.showToast('The other party has hung up');
      },
      onLeaveChannel: (RtcConnection connection, RtcStats stats) {
        LogUtil.w(
            '[onLeaveChannel] connection: ${connection.toJson()} stats: ${stats.toJson()}');
      },
      onRemoteVideoStateChanged: (RtcConnection connection, int remoteUid,
          RemoteVideoState state, RemoteVideoStateReason reason, int elapsed) {
        LogUtil.w(
            '[onRemoteVideoStateChanged] connection: ${connection.toJson()} remoteUid: $remoteUid state: $state reason: $reason elapsed: $elapsed');
      },
    );

    engine.registerEventHandler(_rtcEngineEventHandler);
    await engine.enableVideo();
    await engine.startPreview();
  }

  void showCloseDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomAlertDialog(
          title: "Hang up",
          content: "This a free call. \nAre you sure to hang up now?",
          primaryButtonText: "Keep matching",
          onPrimaryButtonPressed: () {
            Navigator.of(context).pop();
          },
          secondaryButtonText: "hang up",
          onSecondaryButtonPressed: () {
            Navigator.of(context).pop();
            _finishCall();
          },
        );
      },
    );
  }

  void listenCall() {
    isListenedClicked.value = true;
    Future.delayed(const Duration(seconds: 2), () {
      // 延迟 2 秒后执行的代码
      if (isInitialized.value) {
        isPlaying.value = true;
        videoController.play();
        update();
      }
    });
  }

  _deleteFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      try {
        await file.delete();
        print("File deleted: $filePath");
      } catch (e) {
        print("Failed to delete file: $e");
      }
    } else {
      print("File not found: $filePath");
    }
  }

  _finishCall() {
    manager.recalculateIntervalTime();
    manager.resumeLoop();
    Get.back();
  }
}
