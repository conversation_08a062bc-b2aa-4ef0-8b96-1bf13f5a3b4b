import 'package:Chamatch/app/routes/app_pages.dart';
import 'package:Chamatch/http/http_request.dart';
import 'package:Chamatch/model/match_credential_model.dart';
import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/repo/api_hub.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/toast_util.dart';
import 'package:get/get.dart';

import '../../../utils/constants.dart';

class MatchController extends GetxController {
  final userInfoService = Get.find<UserInfoService>();
  final randomAnchor = UserBean.empty().obs;
  final matching = false.obs;
  final matchCredential = MatchCredentialBean.empty().obs;

  getRandomAnchor() async {
    matching.value = true;
    ToastUtil.loading();
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.anchor.randomAnchor,
        method: Method.POST,
        (p0) => UserBean.fromJson(p0));
    ToastUtil.dismiss();
    Future.delayed(const Duration(seconds: 3), () {
      matching.value = false;
    });

    if (result.id != null) {
      Get.toNamed('/video-chat',
          arguments: [result, false, false, Constants.videoSourceMatch]);
    }
  }
}
