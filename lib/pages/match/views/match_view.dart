import 'dart:math';

import 'package:Chamatch/manager/gift_manager.dart';
import 'package:Chamatch/manager/payment_manager.dart';
import 'package:Chamatch/pages/match/controllers/match_controller.dart';
import 'package:Chamatch/widget/fake_animation_anchor_widget.dart';
import 'package:Chamatch/widget/item_recharge_widget.dart';
import 'package:Chamatch/widget/match_radar_widget.dart';
import 'package:Chamatch/widget/theme_container.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

class MatchView extends GetView<MatchController> {
  const MatchView({super.key});

  @override
  Widget build(BuildContext context) {
    return ThemeContainer(
      child: Stack(
        children: [
          // 背景图层
          Positioned.fill(
            child: Image.asset("assets/bg_match.png", fit: BoxFit.cover),
          ),
          Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              _buildTitleWidget(),
              Expanded(
                  child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                      child: SizedBox(
                        width: 150,
                        height: 150,
                        child: MatchRadarWidget(),
                      )),
                  Positioned(child: _buildCenterMatchButtonWidget(context)),

                  Positioned(child: FakeAnimationAnchorWidget()),
                ],
              )),
              _buildBottomRechargeWidget(context)
            ],
          ),
        ],
      ),
    );
  }

  _buildTitleWidget() {
    return Row(
      children: [
        const SizedBox(
          width: 10,
        ),
        Image.asset("assets/ic_circle_v.png", width: 22),
        const SizedBox(
          width: 10,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(10),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset("assets/ic_diamond.png", width: 24),
              Obx(() => Text(
                  "${controller.userInfoService.user.value?.diamond ?? "0"}"))
            ],
          ),
        ),
        const Expanded(
            child: Text(
          "Chamatch",
          style: TextStyle(color: Color(0xff53fe70)),
        )),
        // Image.asset("assets/ic_filter.png", width: 24),
        const SizedBox(
          width: 10,
        ),
      ],
    );
  }

  _buildCenterMatchButtonWidget(BuildContext context) {
    return Positioned(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              if ((controller.userInfoService.user.value?.diamond ?? 0) <= 0) {
                PaymentManager.showRechargeDialog(context);
              } else {
                controller.getRandomAnchor();
              }
            },
            child: Container(
              width: 108,
              height: 108,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(colors: [
                  const Color(0xFF10DB21).withOpacity(0.5),
                  const Color(0xFF23E0BA).withOpacity(0.5)
                ]),
              ),
              child: Container(
                width: 88,
                height: 88,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                      colors: [Color(0xFF10DA21), Color(0xFF22DFB9)]),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Match",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    // Text(
                    //   "Free chances\n23:34:34",
                    //   textAlign: TextAlign.center,
                    //   style: TextStyle(
                    //     color: Colors.white,
                    //     fontSize: 10,
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildBottomRechargeWidget(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.only(left: 10, top: 0, right: 10, bottom: 50),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/bg_match_bottom.png"),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                width: 16,
              ),
              Image.asset(
                "assets/bg_match_bottom_heart.png",
                height: 41,
              ),
              const SizedBox(
                width: 10,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Tap screen to match",
                    style: TextStyle(fontSize: 16),
                  ),
                  Text(
                    "${controller.userInfoService.user.value?.freeRandomMatch} Free matches today",
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  )
                ],
              ),
              const Spacer(),
              InkWell(
                onTap: () {
                  PaymentManager.showRechargeDialog(context);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
                  alignment: Alignment.center,
                  decoration: ShapeDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment(1.00, 0.00),
                      end: Alignment(-1, 0),
                      colors: [Color(0xFF10DA21), Color(0xFF22DFB9)],
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(
                    'Get coins',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ),
              const SizedBox(width: 16)
            ],
          )
        ],
      ),
    );
  }
}
