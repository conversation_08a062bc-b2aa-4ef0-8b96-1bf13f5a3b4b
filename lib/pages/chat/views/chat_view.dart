import 'package:Chamatch/im/custom_message/mikchat_message.dart';
import 'package:Chamatch/widget/CustomNetworkImage.dart';
import 'package:Chamatch/widget/refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../../im/custom_message/mikchat_ask_gift_message.dart';
import '../../../im/custom_message/mikchat_gift_message.dart';
import '../../../manager/gift_manager.dart';
import '../../../model/gift_model.dart';
import '../../../widget/CommonTitleBar.dart';
import '../../../widget/ImageMessageWidget.dart';
import '../controllers/chat_controller.dart';

class ChatView extends GetView<ChatController> {
  const ChatView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏透明
        statusBarIconBrightness: Brightness.light, // 设置状态栏图标为白色
      ),
    );
    return Scaffold(
      body: Stack(
        children: [
          // 背景图片
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16), // 设置圆角
              child: Image.asset(
                'assets/bg_message.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 主内容
          Column(
            children: [
              const SizedBox(
                height: 44,
              ),
              // 公共标题栏
              Obx(() => CommonTitleBar(
                    title: controller.userBean.value.nickName ?? '',
                    titleColor: Colors.transparent,
                    leftIcon: Icons.arrow_back,
                    onLeftPressed: () => Get.back(),
                  )),
              // 消息列表和顶部用户信息
              Obx(() => Expanded(
                    child: SmartRefresher(
                      controller: controller.refreshController,
                      enablePullDown: true,
                      // enablePullUp: true,
                      onRefresh: () {
                        controller.loadMessages(true);
                      },
                      onLoading: () {
                        controller.loadMoreMessages();
                      },
                      child: (controller.isSystemOrCustomService())
                          ? ListView.builder(
                              controller: controller.scrollController,
                              itemCount: controller.messages.length,
                              itemBuilder: (context, index) {
                                return _buildMessageItem(
                                    controller.messages[index - 1]);
                              },
                            )
                          : ListView.builder(
                              controller: controller.scrollController,
                              itemCount: controller.messages.length + 1,
                              itemBuilder: (context, index) {
                                if (index == 0) {
                                  // 用户信息
                                  return GestureDetector(
                                    onTap: () =>
                                        {controller.gotoAnchorDetail()},
                                    child: _buildUserInfo(),
                                  );
                                } else {
                                  // 消息项
                                  return GestureDetector(
                                    onLongPress: () {
                                      _showCustomDialog(context,
                                          controller.messages[index - 1]);
                                    },
                                    child: _buildMessageItem(
                                        controller.messages[index - 1]),
                                  );
                                }
                              },
                            ),
                    ),
                  )),
              // 底部输入框
              _buildBottomInputBar(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/bg_message_top_info.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: Obx(() => Column(
                children: [
                  Row(
                    children: [
                      CustomHeaderImage(
                          imageUrl:
                              controller.userBean.value.headFileName ?? '',
                          width: 50),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${controller.userBean.value.nickName ?? ''}, ${controller.userBean.value.age}',
                            style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                          Text(
                            controller.userBean.value.country ?? '',
                            style: const TextStyle(
                                color: Colors.white54, fontSize: 12),
                          ),
                        ],
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 14,
                      ),
                    ],
                  ),
                  if (controller.userBean.value.signature != null)
                    const SizedBox(height: 10),
                  Text(
                    controller.userBean.value.signature ?? '',
                    style: const TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                  if (controller.imageList.isNotEmpty)
                    const SizedBox(height: 10),
                  SizedBox(
                    height: controller.imageList.isNotEmpty ? 50 : 0,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: controller.imageList.length,
                      itemBuilder: (context, index) {
                        return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: CustomNetworkImage(
                              imageUrl: controller.imageList[index],
                              width: 50,
                              height: 50,
                            ));
                      },
                    ),
                  ),
                ],
              )),
        ));
  }

  Widget _buildMessageItem(RCIMIWMessage message) {
    // 判断是否自己发送的消息
    bool isSelf = message.direction == RCIMIWMessageDirection.send;
    return Align(
      alignment: isSelf ? Alignment.centerRight : Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8),
        // 左右padding 10
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isSelf) // 如果是自己发的消息，头像在右边，所以不需要显示头像
              // 用户信息
              GestureDetector(
                onTap: () => {controller.gotoAnchorDetail()},
                child: CustomHeaderImage(
                    imageUrl: controller.userBean.value.headFileName ?? '',
                    width: 44),
              ),
            if (isSelf) const Expanded(child: Center()),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (isSelf && message.sentStatus == RCIMIWSentStatus.sending)
                  const SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator()),
                if (isSelf && message.sentStatus == RCIMIWSentStatus.failed)
                  const Padding(
                    padding: EdgeInsets.only(right: 8.0),
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 24,
                    ),
                  ),
                _getMessageItem(message, isSelf),
              ],
            ),
            if (isSelf) // 如果是自己发的消息，头像在右边
              CustomHeaderImage(
                  imageUrl:
                      controller.userInfoService.user.value?.headFileName ?? '',
                  width: 44),
          ],
        ),
      ),
    );
  }

  Widget _getMessageItem(RCIMIWMessage message, bool isSelf) {
    if (message is RCIMIWTextMessage) {
      RCIMIWTextMessage rcimiwTextMessage = message;
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelf ? const Color(0xFF1E6C21) : const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(90),
        ),
        constraints: const BoxConstraints(maxWidth: 220),
        // 限制最大宽度
        child: Text(
          rcimiwTextMessage.text ?? '',
          style: const TextStyle(fontSize: 14),
          softWrap: true,
          overflow: TextOverflow.fade, // 超出部分淡出
          textAlign: TextAlign.left,
        ),
      );
    } else if (message is RCIMIWImageMessage) {
      RCIMIWImageMessage rcimiwImageMessage = message;
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: ImageMessageWidget(
          base64String:
              rcimiwImageMessage.thumbnailBase64String ?? '', // 替换成实际的Base64字符串
        ),
      );
    } else if (message is RCIMIWVoiceMessage) {
      RCIMIWVoiceMessage rcimiwVoiceMessage = message;
      return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelf ? const Color(0xFF1E6C21) : const Color(0xFF2B2B2B),
            borderRadius: BorderRadius.circular(90),
          ),
          // 限制最大宽度
          child: GestureDetector(
            onTap: () {
              controller.togglePlay(rcimiwVoiceMessage);
            },
            child: Row(
              children: [
                Text(
                  '${rcimiwVoiceMessage.duration.toString() ?? ''}s',
                  style: const TextStyle(fontSize: 14),
                  softWrap: true,
                  overflow: TextOverflow.fade, // 超出部分淡出
                  textAlign: TextAlign.left,
                ),
                Obx(() => Image.asset(
                      (controller.playVoiceMessageId.value ==
                              rcimiwVoiceMessage.messageId)
                          ? controller
                              .images[controller.currentImageIndex.value]
                          : 'assets/rc_voice_send_play3.png',
                      width: 16,
                      height: 16,
                    )),
              ],
            ),
          ));
    } else if (message is MikChatAskGiftMessage) {
      MikChatAskGiftMessage mikChatAskGiftMessage = message;
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelf ? const Color(0xFF1E6C21) : const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(90),
        ),
        constraints: const BoxConstraints(maxWidth: 220),
        // 限制最大宽度
        child:Row(
          children: [
            const Text(
              'can you give me one ',
              style: TextStyle(fontSize: 14),
              softWrap: true,
              overflow: TextOverflow.fade, // 超出部分淡出
              textAlign: TextAlign.left,
            ),
            CustomHeaderImage(imageUrl: mikChatAskGiftMessage.giftUrl ?? '', width: 24)
          ],
        )

      );
    } else if (message is MikChatGiftMessage) {
      MikChatGiftMessage mikChatGiftMessage = message;
      return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelf ? const Color(0xFF1E6C21) : const Color(0xFF2B2B2B),
            borderRadius: BorderRadius.circular(90),
          ),
          child: Column(
            children: [
              const Text(
                'you send X1',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.left,
              ),
              CustomHeaderImage(imageUrl: mikChatGiftMessage.giftUrl ?? '', width: 54)
            ],
          ));
    } else {
      return const Text('unknown msg');
    }
  }

  Widget _buildBottomInputBar(BuildContext context) {
    // 获取底部安全区域高度
    final bottomPadding = MediaQuery.of(context).viewPadding.bottom;
    return Container(
      padding: EdgeInsets.fromLTRB(
        16.0,
        10.0,
        16.0,
        10.0 + bottomPadding, // 底部添加安全区域高度的内边距
      ),
      color: const Color(0xFF2b2b2b),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller.textController,
                  decoration: const InputDecoration(
                    hintText: 'Say hi...',
                    hintStyle: TextStyle(color: Colors.white54),
                    border: InputBorder.none,
                  ),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.send, color: Colors.green),
                onPressed: controller.sendTextMessage,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                  icon: Image.asset(
                    'assets/ic_message_voice.png',
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () => controller.sendVoiceMessage(context)),
              IconButton(
                  icon: Image.asset(
                    'assets/ic_message_image.png',
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () => controller.sendImageMessage(context)),
              // IconButton(
              //     icon: Image.asset(
              //       'assets/ic_message_emoji.png',
              //       width: 24,
              //       height: 24,
              //     ),
              //     onPressed: controller.showEmojiPicker),
              if (!controller.isSystemOrCustomService())
                IconButton(
                    icon: Image.asset(
                      'assets/ic_message_gift.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: () {
                      controller.showGiftPopup(context);
                    }),
              if (!controller.isSystemOrCustomService())
                IconButton(
                    icon: Image.asset(
                      'assets/ic_message_video.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: controller.startVideoCall),
            ],
          ),
          Obx(() {
            switch (controller.inputModel.value) {
              case 'text':
                return const Center();
              case 'voice':
                return GestureDetector(
                  onLongPressStart: (details) {
                    controller.sendVoiceMessage(context);
                  },
                  onLongPressMoveUpdate: (details) {},
                  onLongPressEnd: (details) {},
                  child: Container(
                    width: 70,
                    height: 70,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.blue,
                    ),
                    child: const Icon(Icons.mic, color: Colors.white, size: 30),
                  ),
                );
              default:
                return const Center();
            }
          }),
        ],
      ),
    );
  }

  void _showCustomDialog(BuildContext context, RCIMIWMessage message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (message is RCIMIWTextMessage)
                SizedBox(
                  height: 40,
                  child: ListTile(
                    leading: const Icon(Icons.copy),
                    title: const Text("复制消息"),
                    onTap: () {
                      controller.copyMessage(message);
                      Navigator.pop(context); // 关闭弹窗
                    },
                  ),
                ),
              if (message is RCIMIWTextMessage) const Divider(),
              SizedBox(
                height: 40,
                child: ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text("删除消息"),
                  onTap: () {
                    Navigator.pop(context);
                    controller.deleteLocalMessages(message);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
