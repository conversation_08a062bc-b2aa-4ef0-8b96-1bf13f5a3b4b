import 'dart:convert';
import 'dart:typed_data';

import 'package:Chamatch/im/custom_message/mikchat_gift_message.dart';
import 'package:Chamatch/manager/gift_manager.dart';
import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_sound/public/flutter_sound_recorder.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:record/record.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../../engine/event_bus.dart';
import '../../../http/http_request.dart';
import '../../../im/custom_message/mikchat_ask_gift_message.dart';
import '../../../im/im_engine.dart';
import '../../../im/media/rong_media.dart';
import '../../../im/wdiget/record_view.dart';
import '../../../model/gift_model.dart';
import '../../../repo/api_hub.dart';
import '../../../utils/constants.dart';

class ChatController extends GetxController {
  final TextEditingController textController =
      TextEditingController(); // 输入框控制器
  final ScrollController scrollController = ScrollController(); // 滚动控制器
  final RefreshController refreshController = RefreshController(); // 刷新控制器
  final userBean = UserBean.empty().obs;
  final imageList = <String>[].obs;
  final userInfoService = Get.find<UserInfoService>();

  // 语音录制控制器
  FlutterSoundRecorder? _recorder;
  bool isRecording = false;
  String? recordedFilePath;

  static String inputModelText = 'text';
  static String inputModelVoice = 'voice';
  final inputModel = inputModelText.obs;

  final RxBool isPlaying = false.obs; // 播放状态
  late AudioPlayer _audioPlayer;
  final currentImageIndex = 0.obs;
  List<String> images = [
    'assets/rc_voice_send_play3.png',
    'assets/rc_voice_send_play1.png',
    'assets/rc_voice_send_play2.png',
  ];

  // 消息列表
  final RxList<RCIMIWMessage> messages = <RCIMIWMessage>[].obs;
  final count = 0.obs;

  final playVoiceMessageId = 0.obs;

  @override
  void onInit() {
    super.onInit();
    UserBean argUserBean = Get.arguments;
    userBean.value = argUserBean;
    _audioPlayer = AudioPlayer();
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        isPlaying.value = false;
        currentImageIndex.value = 0;
        playVoiceMessageId.value = 0;
      }
    });
    LogUtil.e(
        "vvvvvvv  ${userBean.value.nickName}  ${userBean.value.headFileName}");
    if (userBean.value.id != null) {
      loadMessages(false);
      if (!isSystemOrCustomService()) {
        getAnchorLitDetail(userBean.value.id!);
      }
      bus.on(EventBus.eventReceiveMessage, _refreshConversation);
    }
  }

  @override
  void onClose() {
    bus.emit(
        EventBus.eventRefreshConversation, {"targetId": userBean.value.id});
    bus.off(EventBus.eventReceiveMessage);
    _audioPlayer.dispose();
    super.onClose();
  }

  bool isSystemOrCustomService() {
    return userBean.value.id == Constants.rongYunCustomServiceId ||
        userBean.value.id == Constants.rongYunSystemId;
  }

  _refreshConversation(arg) {
    RCIMIWMessage message = arg as RCIMIWMessage;
    if (message.targetId == userBean.value.id) {
      // if(message is MikChatAskGiftMessage){
      //   insertMessage(message);
      // }
      loadMoreMessages();
    }
  }

  // Future insertMessage(RCIMIWMessage msg) async {
  //   msg.direction = RCIMIWMessageDirection.receive;
  //   msg.receivedStatus = RCIMIWReceivedStatus.unread;
  //
  //   IRCIMIWInsertMessageCallback? callback = IRCIMIWInsertMessageCallback(onMessageInserted: (code, message) {
  //     LogUtil.e("vvvvvvvvv $code $message}");
  //     });
  //   int? code = await IMEngineManager()
  //       .engine
  //       ?.insertMessage(msg, callback: callback);
  // }

  Future<void> getAnchorLitDetail(String anchorId) async {
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.anchor.anchorlitDetail,
        method: Method.GET,
        params: {'id': anchorId},
        (p0) => UserBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});

    if (result.photos != null) {
      imageList.assignAll(result.photos!);
    }
    userBean.value = result;
    scrollToBottom();
  }

  final record = AudioRecorder();

  // 发送语音消息
  Future sendVoiceMessage(BuildContext buildContext) async {
    if (userBean.value.id == null) {
      EasyLoading.showToast("targetId 为空");
      return;
    }
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = userBean.value.id ?? '';
    var status = await Permission.microphone.request();
    if (status.isGranted) {
      showDialog(
          context: buildContext,
          builder: (context) {
            return Center(
                child: RecordView(
              onPressed: () {
                RCIWMediaUlits.startRecordAudio(record);
              },
              onCanceled: () {
                Navigator.of(context).pop();
                RCIWMediaUlits.stopRecordAudio(
                    record, (path, duration) async {});
                EasyLoading.showToast("录音被取消");
              },
              onFinished: () {
                RCIWMediaUlits.stopRecordAudio(record, (path, duration) async {
                  LogUtil.e("vvvvvvvvv $path  $duration");
                  if (path != null && (duration ?? 0) > 0) {
                    RCIMIWVoiceMessage? msg = await IMEngineManager()
                        .engine
                        ?.createVoiceMessage(
                            type, targetId, null, path, duration!);
                    msg?.extra = _genMessageExtra();
                    msg?.userInfo = _genMessageUserInfo();
                    _sendMessage(msg);
                  }
                });
                Navigator.of(context).pop();
              },
            ));
          });
    } else if (status.isPermanentlyDenied) {
      openAppSettings();
    }
  }

  Future sendImageMessage(BuildContext context) async {
    RCIMIWConversationType type = RCIMIWConversationType.private;
    RCIWMediaUlits.showImagePicker(context, (XFile? file) async {
      if (file != null) {
        Uint8List fileBytes = await file.readAsBytes();
        String base64 = base64Encode(fileBytes);
      }
      RCIMIWImageMessage? message = await IMEngineManager()
          .engine
          ?.createImageMessage(
              type, userBean.value.id ?? '', null, file?.path ?? '');
      message?.original = false;
      message?.extra = _genMessageExtra();
      message?.userInfo = _genMessageUserInfo();
      _sendMessage(message);
    });
  }

  // 加载消息
  Future<void> loadMessages(bool isUserForce) async {
    var sentTime = 0;
    if (messages.isEmpty) {
      sentTime = 0;
    } else {
      sentTime = messages.first.sentTime ?? 0;
    }
    await _getMessages(sentTime, RCIMIWTimeOrder.before, isUserForce);
  }

  // 加载更多消息
  Future<void> loadMoreMessages() async {
    var sentTime = 0;
    if (messages.isEmpty) {
      sentTime = 0;
    } else {
      sentTime = messages.last.sentTime ?? 0;
    }
    await _getMessages(sentTime, RCIMIWTimeOrder.after, false);
  }

  // 滚动到底部
  void scrollToBottom() {
    scrollController.animateTo(
      scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

// 语音录制
  void startVoiceRecording() {
    // 实现语音录制逻辑
    inputModel.value = inputModelVoice;
  }

  // 图片选择
  void pickImage() {
    // 实现图片选择逻辑
  }

  // 表情选择
  void showEmojiPicker() {
    // 实现表情选择逻辑
  }

  // 礼物弹窗
  void showGiftPopup(BuildContext context) {
    // 实现礼物弹窗逻辑
    GiftManager.showGiftDialog(context, callback: (GiftBean bean) {
      _sendGiftToServer(bean);
    });
  }

  _sendGiftToServer(GiftBean bean) async {
   await HttpRequest.request<dynamic>(
        APIHub.anchor.giftive,
        method: Method.POST,
        params: {
          'anchorId': userBean.value.id ?? '',
          "giveNum": '1',
          "id": bean.id,
          "userRole": "1",
          "source": 0,
        },
        (p0) => {_sendGiftMessage(bean)},
        exception: (e) => {debugPrint('parse exception $e')});
  }

  _sendGiftMessage(GiftBean bean) {
    int newDiamond = userInfoService.user.value?.diamond ?? 0 - bean.giftPrice;
    userInfoService.user.update((user) {
      user?.diamond = newDiamond > 0 ? newDiamond : 0;
    });
    RCIMIWConversationType type = RCIMIWConversationType.private;
    MikChatGiftMessage msg = MikChatGiftMessage(
        type,
        userBean.value.id ?? '',
        '',
        '',
        bean.giftCode,
        '',
        '',
        '',
        '1',
        userBean.value.nickName,
        '1',
        bean.giftIcon);
    msg.extra = _genMessageExtra();
    msg.userInfo = _genMessageUserInfo();
    _sendMessage(msg);
  }

  // 视频通话
  void startVideoCall() {
    Get.toNamed('/video-chat',
        arguments: [userBean.value, false, false, Constants.videoSourceNormal]);
  }

  _getMessages(int sentTime, RCIMIWTimeOrder order, bool isUserForce,
      {RCIMIWMessageOperationPolicy policy =
          RCIMIWMessageOperationPolicy.localRemote}) async {
    RCIMIWConversationType type = RCIMIWConversationType.private;
    String targetId = userBean.value.id ?? '';
    IRCIMIWGetMessagesCallback? callback =
        IRCIMIWGetMessagesCallback(onSuccess: (List<RCIMIWMessage>? t) {
      if (order == RCIMIWTimeOrder.before) {
        if (t != null) {
          messages.insertAll(0, t.reversed);
        }
        refreshController.refreshCompleted();
      } else {
        if (t != null) {
          messages.addAll(t);
        }
        refreshController.loadComplete();
      }
      // 强制触发更新
      LogUtil.e('getMessages messages: ${messages.length}');
      if (order == RCIMIWTimeOrder.before && isUserForce) {
        LogUtil.d("user pull down");
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          scrollToBottom();
        });
      }
      if (messages.last.sentTime != null) {
        _clearUnreadCount(messages.last.sentTime!);
      }
    }, onError: (int? code) {
      LogUtil.e('getMessages failed: $code');
    });
    int? code = await IMEngineManager().engine?.getMessages(
        type, targetId, null, sentTime, order, policy, 20,
        callback: callback);
    LogUtil.e('getMessages code: $code');
  }

  RCIMIWUserInfo? _genMessageUserInfo() {
    RCIMIWUserInfo? userInfo;
    if (userBean.value.id != null) {
      userInfo = RCIMIWUserInfo.create();
      userInfo.userId = userBean.value.id;
      userInfo.portrait = userBean.value.headFileName;
      userInfo.name = userBean.value.nickName;
    }
    return userInfo;
  }

  String _genMessageExtra() {
    UserBean extraUser = UserBean.empty();
    extraUser.id = userBean.value.id;
    extraUser.nickName = userBean.value.nickName;
    extraUser.headFileName = userBean.value.headFileName;
    extraUser.level = userBean.value.level;
    extraUser.userCode = userBean.value.userCode;
    extraUser.userRole = "2";
    return jsonEncode(extraUser);
  }

  _genTimeMessage() {
    final int currentSentTime = DateTime.now().millisecondsSinceEpoch;
  }

  Future sendTextMessage() async {
    var text = textController.text;
    if (text.isEmpty) {
      EasyLoading.showToast("请输入发送内容");
      return;
    }
    RCIMIWConversationType conversationType = RCIMIWConversationType.private;
    String targetId = userBean.value.id ?? '';
    RCIMIWTextMessage? textMessage =
        await IMEngineManager().engine?.createTextMessage(
              conversationType,
              targetId,
              null,
              text,
            );

    if (textMessage == null) {
      EasyLoading.showToast("消息创建失败");
      return;
    }
    textMessage.extra = _genMessageExtra();
    textMessage.userInfo = _genMessageUserInfo();
    _sendMessage(textMessage);
  }

  _sendMessage(RCIMIWMessage? message) async {
    if (message == null) {
      EasyLoading.showToast("message 不合法");
      return;
    }

    if (message.conversationType == RCIMIWConversationType.chatroom ||
        message.conversationType == RCIMIWConversationType.system) {
      message.expansion = null;
    } else {
      message.expansion = {};
    }
    int? code = -1;
    Map<String, String> resultCode = {};
    if (message is RCIMIWMediaMessage) {
      RCIMIWSendMediaMessageListener? listener =
          RCIMIWSendMediaMessageListener(onMediaMessageSaved: (message) {
        messages.add(message!);
      }, onMediaMessageSending: (message, progress) {
        LogUtil.e("vvvvvvv ${message?.messageId}  $progress");
        _refreshMessageItem(message);
        scrollToBottom();
      }, onMediaMessageSent: (code, message) {
        LogUtil.e("vvvvvvv messageSent ${message?.messageId}");
        _refreshMessageItem(message);
        scrollToBottom();
      }, onSendingMediaMessageCanceled: (message) {
        _removeMessageItem(message);
      });
      code = await IMEngineManager()
          .engine
          ?.sendMediaMessage(message, listener: listener);
      resultCode["listener"] = "sendMediaMessage";
    } else {
      RCIMIWSendMessageCallback? callback =
          RCIMIWSendMessageCallback(onMessageSaved: (message) {
            LogUtil.e('_sendMessage onMessageSaved ${message?.toString()}');
        textController.text = '';
        messages.add(message!);
      }, onMessageSent: (code, message) {
        _refreshMessageItem(message);
        scrollToBottom();
        LogUtil.e('_sendMessage onMessageSent ${message?.toString()}');
      });
      LogUtil.e('vvvvvvvvvv $message');
      code = await IMEngineManager()
          .engine
          ?.sendMessage(message, callback: callback);
    }
    resultCode["code"] = (code ?? -1).toString();
    bus.emit("rong_im_listener", resultCode);
  }

  _refreshMessageItem(RCIMIWMessage? message) {
    int index = messages.indexWhere(
        (conversation) => conversation.messageId == message?.messageId);
    if (index != -1) {
      messages[index] = message!;
    }
  }

  _removeMessageItem(RCIMIWMessage? message) {
    int index = messages.indexWhere(
        (conversation) => conversation.messageId == message?.messageId);
    if (index != -1) {
      messages.removeAt(index);
    }
  }

  _clearUnreadCount(int lastMsgTime) async {
    RCIMIWConversationType type = RCIMIWConversationType.private;
    IRCIMIWClearUnreadCountCallback? callback =
        IRCIMIWClearUnreadCountCallback(onUnreadCountCleared: (int? code) {
      Map<String, String> arg = {};
      arg["listener"] = "clearUnreadCount-onUnreadCountCleared";
      arg["code"] = code.toString();

      bus.emit("rong_im_listener", arg);
    });

    int? code = await IMEngineManager().engine?.clearUnreadCount(
        type, userBean.value.id ?? '', null, lastMsgTime,
        callback: callback);
    LogUtil.e("_clearUnreadCount, $code");
  }

  String _formatJson(jsonObject) {
    if (jsonObject == null) {
      return "";
    }
    const JsonEncoder encoder = JsonEncoder.withIndent('    ');
    return encoder.convert(jsonObject);
  }

  gotoAnchorDetail() {
    Get.toNamed('/profile', arguments: userBean.value);
  }

  void copyMessage(RCIMIWTextMessage message) {
    if (message.text != null) {
      Clipboard.setData(ClipboardData(text: message.text!)).then((_) {
        EasyLoading.showToast("已复制到剪贴板：$message.text");
      });
    }
  }

  Future deleteLocalMessages(RCIMIWMessage message) async {
    if (message.messageId == null) {
      EasyLoading.showToast("messageIds 为空");
      return;
    }
    List<RCIMIWMessage> removeMessages = [];
    removeMessages.add(message);

    IRCIMIWDeleteLocalMessagesCallback? callback =
        IRCIMIWDeleteLocalMessagesCallback(
            onLocalMessagesDeleted: (code, callbackMessage) {
      if (code == 0) {
        messages.remove(message);
      } else {
        EasyLoading.showToast('删除失败');
      }
    });
    int? code = await IMEngineManager()
        .engine
        ?.deleteLocalMessages(removeMessages, callback: callback);
  }

  // 动画效果，切换图片
  void _animateImages() {
    int index = 0;
    Future.doWhile(() async {
      currentImageIndex.value = index;
      index = (index + 1) % images.length; // 循环切换图片
      await Future.delayed(const Duration(milliseconds: 300)); // 每秒切换一次图片
      return true;
    });
  }

  // 开始/暂停语音
  void togglePlay(RCIMIWVoiceMessage rcimiwVoiceMessage) async {
    playVoiceMessageId.value = rcimiwVoiceMessage.messageId ?? 0;
    if (isPlaying.value) {
      await _audioPlayer.pause();
    } else {
      if (rcimiwVoiceMessage.remote != null) {
        await _audioPlayer.setUrl(rcimiwVoiceMessage.remote!);
        _audioPlayer.play();
        // 动画效果，切换图片
        _animateImages();
      }
    }
    isPlaying.value = !isPlaying.value;
  }
}
