import 'package:Chamatch/controller/user_controller.dart';
import 'package:Chamatch/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../http/http_request.dart';
import '../../../model/page_model.dart';
import '../../../model/user_model.dart';
import '../../../repo/api_hub.dart';
import '../../../service/UserInfoService.dart';
import '../../../storage/storage.dart';
import '../../../utils/Hive_util.dart';

class HomeController extends GetxController
    with GetSingleTickerProviderStateMixin {
  UserController userController = Get.find<UserController>();

  late TabController tabController;
  final anchorItems = <UserBean>[].obs;
  int currentPage = 1;
  final userInfoService = Get.find<UserInfoService>();

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 3, vsync: this);
    tabController.addListener(() {
      fetchData(true);
    });

    fetchData(true);
  }

  Future<void> fetchData(bool refresh) async {
    currentPage = refresh ? 1 : currentPage + 1;
    _getAnchorList(tabController.index, currentPage);

    if (userInfoService.user.value?.id?.isNotEmpty == true) {
      userController.getUserDetail((HiveUtil.get<UserBean>(StorageKeys.USER_KEY,
                  fromJson: UserBean.fromJson))
              ?.id ??
          "");
    }
  }

  _getAnchorList(int type, int current) async {
    String url = APIHub.anchor.recommendList;
    switch (type) {
      case 0:
        url = APIHub.anchor.popularList;
        break;
      case 1:
        url = APIHub.anchor.newList;
        break;
      case 2:
        url = APIHub.anchor.followList;
        break;
    }

    PageBean<UserBean> pageBean = await HttpRequest.request(
        url,
        method: Method.POST,
        params: {"current": "$current", "size": "20"},
        (p0) => PageBean<UserBean>.fromJson(p0, UserBean.fromJson));
    if (current == 1) {
      anchorItems.clear();
      anchorItems.assignAll(pageBean.records);
    } else {
      anchorItems.addAll(pageBean.records);
    }
    // anchorItems.refresh();
  }

  void gotoVideo(UserBean bean) {
    Get.toNamed('/video-chat',
        arguments: [bean, false, false, Constants.videoSourceNormal]);
  }

  void gotoConversation(UserBean bean) {
    Get.toNamed('/chat', arguments: bean);
  }
}
