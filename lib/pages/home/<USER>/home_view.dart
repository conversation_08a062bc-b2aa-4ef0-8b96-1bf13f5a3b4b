import 'package:Chamatch/utils/common_util.dart';
import 'package:Chamatch/widget/refresh.dart';
import 'package:Chamatch/widget/theme_scaffold.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

import '../../../manager/payment_manager.dart';
import '../../../model/user_model.dart';
import '../../../widget/CustomNetworkImage.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return ThemeScaffold(
        title: "Chamatch",
        titleColor: Colors.transparent,
        appBarBottom: TabBar(
          controller: controller.tabController,
          tabs: const [
            Tab(text: '🔥Hot'),
            Tab(text: 'New'),
            Tab(text: 'Follow'),
          ],
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/ic_diamond.png",
                  width: 20,
                  height: 20,
                ),
                const SizedBox(width: 5),
                Obx(() => Text(
                    "${controller.userInfoService.user.value?.diamond}",
                    style: const TextStyle(color: Colors.white)))
              ],
            ),
          ),
        ],
        body: TabBarView(
          controller: controller.tabController,
          children: [
            Refresh(
                child: Obx(() => GridView.builder(
                    padding: const EdgeInsets.all(8.0),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 8.0,
                      mainAxisSpacing: 8.0,
                      childAspectRatio: 0.8,
                    ),
                    itemCount: controller.anchorItems.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () => Get.toNamed('/profile',
                            arguments: controller.anchorItems[index]),
                        child: _buildItem(context, controller.anchorItems[index]),
                      );
                    })),
                onRefresh: () {
                  controller.fetchData(true);
                },
                onLoad: () {
                  // fetchData(false);
                  controller.fetchData(false);
                })
          ],
        ));
  }

  _buildItem(BuildContext context, UserBean bean) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
      child: Stack(
        children: [
          CustomNetworkImage(
              imageUrl: bean.headFileName ?? '',
              width: double.infinity,
              height: double.infinity,
              borderRadius: 10 // 自定义圆角值
              ),
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
              decoration: BoxDecoration(
                  color: Colors.black87.withOpacity(0.5),
                  borderRadius: const BorderRadius.all(Radius.circular(10))),
              child: Text(
                CommonUtil.formatOnlineStatusText(bean.onlineStatus),
                style: const TextStyle(color: Colors.white, fontSize: 10),
              ),
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 90),
                    child: Text(
                      bean.nickName ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          fontWeight: FontWeight.bold),
                    )),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 3),
                      decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.2),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10))),
                      child: Text(
                        '${bean.age}',
                        style: TextStyle(
                            color: Colors.white.withOpacity(0.6), fontSize: 9),
                      ),
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 3),
                      decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.2),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10))),
                      child: Text(
                        '${bean.country}',
                        style: TextStyle(
                            color: Colors.white.withOpacity(0.6), fontSize: 9),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          Positioned(
            bottom: 8,
            right: 8,
            child: Row(
              children: [
                bean.onlineStatus == "1"
                    ? Container(
                        padding: const EdgeInsets.all(10),
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                              colors: [Color(0xFF10DB21), Color(0xFF23E0BA)]),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            if ((controller.userInfoService.user.value?.diamond ?? 0) <= 0) {
                              PaymentManager.showRechargeDialog(context);
                            } else {
                              controller.gotoVideo(bean);
                            }

                          },
                          child: Image.asset(
                            "assets/ic_anchor_video.png",
                            width: 21,
                            height: 19,
                          ),
                        ))
                    : Container(
                        padding: const EdgeInsets.all(10),
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                              colors: [Color(0xfff9db1b), Color(0xfff46b01)]),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            controller.gotoConversation(bean);
                          },
                          child: Image.asset(
                            "assets/ic_anchor_msg.png",
                            width: 20,
                            height: 19,
                          ),
                        ))
              ],
            ),
          ),
        ],
      ),
    );
  }
}
