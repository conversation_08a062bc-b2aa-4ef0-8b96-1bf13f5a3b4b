import 'package:Chamatch/widget/CustomNetworkImage.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/video_chat_controller.dart';

class VideoChatView extends GetView<VideoChatController> {
  const VideoChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(() {
        return Stack(
          children: [
            if (controller.joinUserId.value > 0) ...[
              AgoraVideoView(
                controller: VideoViewController.remote(
                  rtcEngine: controller.engine,
                  canvas:
                  VideoCanvas(uid: controller.joinUserId.value),
                  connection: RtcConnection(
                      channelId: controller.channelId.value),
                  useFlutterTexture: controller.isUseFlutterTexture,
                  useAndroidSurfaceView:
                  controller.isUseAndroidSurfaceView,
                ),
              ),
              Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                      padding: const EdgeInsets.only(top: 160, right: 10),
                      child: SizedBox(
                          width: 90,
                          height: 120,
                          child: AgoraVideoView(
                            controller: VideoViewController(
                              rtcEngine: controller.engine,
                              canvas: const VideoCanvas(uid: 0),
                              useFlutterTexture: controller.isUseFlutterTexture,
                              useAndroidSurfaceView: controller.isUseAndroidSurfaceView,
                            ),
                            onAgoraVideoViewCreated: (viewId) {
                              controller.engine.startPreview();
                            },
                          ))))
            ],
            if (controller.joinUserId.value <= 0) ...[
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center, // 垂直方向居中
                  crossAxisAlignment: CrossAxisAlignment.center, // 水平方向居中
                  children: [
                    if (!controller.isIncomingCall.value || controller.isIncomingCallListened.value)
                      const Text(
                        'Connecting...',
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    const SizedBox(height: 16),
                    Stack(
                      children: [
                        Column(
                          children: [
                            CustomNetworkImage(
                                imageUrl: controller.anchorBigImage.value,
                                width: MediaQuery.of(context).size.width - 20,
                                height: 520,
                                borderRadius: 12),
                            const SizedBox(
                              height: 35,
                            )
                          ],
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Stack(
                            children: [
                              Image.asset(
                                'assets/bg_call_center_diamond.png',
                                width: 84,
                                height: 22,
                              ),
                              SizedBox(
                                  width: 84,
                                  height: 24,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'assets/ic_diamond.png',
                                        width: 20,
                                        height: 14,
                                      ),
                                      Text(
                                          '${controller.anchorInfo.value.videoPrice.toString()}/min',
                                          style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white))
                                    ],
                                  ))
                            ],
                          ),
                        ),
                        Positioned(
                            bottom: 0,
                            left: (controller.isIncomingCall.value && !controller.isIncomingCallListened.value)
                                ? 58
                                : MediaQuery.of(context).size.width / 2 - 54,
                            child: GestureDetector(
                              onTap: () {
                                controller.showCloseDialog(context);
                              },
                              child: Image.asset(
                                'assets/ic_call_cancel.png',
                                width: 70,
                                height: 70,
                              ),
                            )),
                        if (controller.isIncomingCall.value && !controller.isIncomingCallListened.value)
                          Positioned(
                            bottom: 0,
                            right: 58,
                            child: GestureDetector(
                                onTap: () {
                                  controller.listenCall();
                                },
                                child: Image.asset(
                                  'assets/ic_call_listen.png',
                                  width: 70,
                                  height: 70,
                                )),
                          )
                      ],
                    )
                  ],
                ),
              ),
            ],
            Positioned(
                top: 60,
                left: 10,
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0x4D000000),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 5),
                        child: Row(
                          children: [
                            CustomHeaderImage(
                                imageUrl:
                                    controller.anchorInfo.value.headFileName ??
                                        '',
                                width: 35),
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              children: [
                                Text(controller.anchorInfo.value.nickName ?? '',
                                    style: const TextStyle(
                                        fontSize: 16, color: Colors.white)),
                                Text(
                                    '${controller.anchorInfo.value.country},${controller.anchorInfo.value.age}',
                                    style: const TextStyle(
                                        fontSize: 12, color: Colors.white))
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      controller.timeText.value,
                      style: const TextStyle(color: Colors.white, fontSize: 20),
                    ),
                  ],
                )),
            Positioned(
                top: 60,
                right: 10,
                child: SizedBox(
                  height: 27,
                  child: controller.joinUserId.value <= 0
                      ? Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFF101010),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset('assets/ic_diamond.png'),
                                Text(
                                    '${controller.anchorInfo.value.videoPrice.toString()}/min',
                                    style: const TextStyle(
                                        fontSize: 12, color: Colors.white))
                              ],
                            ),
                          ))
                      : Row(
                          children: [
                            // GestureDetector(
                            //     onTap: () {
                            //       controller.showReportDialog();
                            //     },
                            //     child: Image.asset(
                            //       'assets/ic_call_report.png',
                            //       width: 20,
                            //       height: 20,
                            //     )),
                            const SizedBox(
                              width: 20,
                            ),
                            GestureDetector(
                                onTap: () {
                                  controller.showCloseDialog(context);
                                },
                                child: Image.asset(
                                  'assets/ic_call_close.png',
                                  width: 20,
                                  height: 20,
                                )),
                          ],
                        ),
                )),
          ],
        );
      }),
    );
  }
}
