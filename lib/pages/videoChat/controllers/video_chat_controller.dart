import 'dart:async';
import 'dart:io';

import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/log.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../engine/event_bus.dart';
import '../../../http/http_request.dart';
import '../../../manager/IncomingAnchorManager.dart';
import '../../../model/channel_model.dart';
import '../../../model/video_deduct_model.dart';
import '../../../repo/api_hub.dart';
import '../../../utils/constants.dart';
import '../../../widget/CustomAlertDialog.dart';

class VideoChatController extends GetxController {
  late final RtcEngine engine;
  late final RtcEngineEventHandler _rtcEngineEventHandler;

  bool isUseFlutterTexture = false;
  bool isUseAndroidSurfaceView = false;
  bool isJoined = false,
      switchRender = true,
      openCamera = true,
      muteAllRemoteVideo = false;

  final switchCamera = true.obs;
  final muteCamera = true.obs;
  final channelId = ''.obs;
  final joinUserId = 0.obs;
  final anchorInfo = UserBean.empty().obs;
  final isConnecting = false.obs;
  final anchorBigImage = ''.obs;

  final userService = Get.find<UserInfoService>();
  String source = Constants.videoSourceNormal;
  final isIncomingCall = false.obs;
  final isIncomingCallListened = false.obs;
  Timer? _countDownTimer;
  Timer? _callTimer;
  int _seconds = 30;
  int _secondsElapsed = 0;
  final userInfoService = Get.find<UserInfoService>();

  final timeText = ''.obs;
  final manager = IncomingAnchorManager();
  @override
  void onInit() {
    super.onInit();
    UserBean argUserBean = Get.arguments[0];
    anchorInfo.value = argUserBean;
    _setAnchorBigImg();
    bool useMatchCard = Get.arguments[1] ?? false;
    isIncomingCall.value = Get.arguments[2] ?? false;
    source = Get.arguments[3] ?? Constants.videoSourceNormal;
    if (anchorInfo.value.id != null) {
      _getAnchorLitDetail(anchorInfo.value.id!);
      _initEngine();
      if (!isIncomingCall.value) {
        _genVideoChannel(anchorInfo.value.id!, useMatchCard);
      }
    }
    manager.pauseLoop();
    bus.on(EventBus.eventVideoReject, _receiveRejectVideoMsg);
  }

  @override
  void onReady() {
    super.onReady();
    _startCountdown();
  }

  _receiveRejectVideoMsg(arg) async {
    String receive = arg as String;
    if(receive == channelId.value){
      _finishCall();
    }
  }

  _setAnchorBigImg() {
    if (anchorInfo.value.photos != null &&
        anchorInfo.value.photos!.isNotEmpty) {
      anchorBigImage.value = anchorInfo.value.photos![0];
    } else {
      anchorBigImage.value = anchorInfo.value.headFileName ?? '';
    }
  }

  @override
  void onClose() async {
    super.onClose();
    engine.unregisterEventHandler(_rtcEngineEventHandler);
    await engine.leaveChannel();
    await engine.release();
  }

  Future<void> _getAnchorLitDetail(String anchorId) async {
    UserBean result = await HttpRequest.request<UserBean>(
        APIHub.anchor.anchorlitDetail,
        method: Method.GET,
        params: {'id': anchorId},
        (p0) => UserBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});

    anchorInfo.value = result;
    _setAnchorBigImg();
  }

  Future<void> _genVideoChannel(String anchorId, bool useMatchCard) async {
    ChannelBean result = await HttpRequest.request<ChannelBean>(
        APIHub.anchor.genVideoChannel,
        method: Method.POST,
        params: {
          'userId': anchorId,
          'userRole': '1',
          'freeFlag': useMatchCard ? "1" : "0",
          'sourceType': source
        },
        (p0) => ChannelBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e'), _finishCall()});

    if (result.channelId != null) {
      channelId.value = result.channelId!;
      _getVideoRTCToken(result.channelId!);
    } else {
      LogUtil.e("genVideoChannel failed ");
    }
  }

  Future<void> _getVideoRTCToken(String channelId) async {
    String result = await HttpRequest.request<String>(
        APIHub.anchor.getVideoRtcToken,
        method: Method.POST,
        queryData: {
          'channelId': channelId,
          'userCode': userService.user.value?.userCode ?? ''
        },
        (p0) => p0,
        exception: (e) => {debugPrint('parse exception $e')});

    await engine.joinChannel(
      token: result,
      channelId: channelId,
      uid: int.tryParse(userService.user.value?.userCode ?? '0') ?? 0,
      options: const ChannelMediaOptions(
        // 自动订阅所有视频流
        autoSubscribeVideo: true,
        // 自动订阅所有音频流
        autoSubscribeAudio: true,
        // 发布摄像头采集的视频
        publishCameraTrack: true,
        // 发布麦克风采集的音频
        publishMicrophoneTrack: true,
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
      ),
    );
  }

  Future<void> _initEngine() async {
    engine = createAgoraRtcEngine();
    await engine.initialize(const RtcEngineContext(
      appId: '9bebac55156e49a7ada7616f1ea20399',
    ));
    _rtcEngineEventHandler = RtcEngineEventHandler(
      onError: (ErrorCodeType err, String msg) {
        LogUtil.e('[onError] err: $err, msg: $msg');
        EasyLoading.showToast('The other party has hung up');
        _finishCall();
      },
      onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
        LogUtil.w(
            '[onJoinChannelSuccess] connection: ${connection.toJson()} elapsed: $elapsed');
        //用户成功加入频道 推送消息给对方
        _postVideoPush(channelId.value);
      },
      onUserJoined: (RtcConnection connection, int rUid, int elapsed) {
        LogUtil.w(
            '[onUserJoined] connection: ${connection.toJson()} remoteUid: $rUid elapsed: $elapsed');
        joinUserId.value = rUid;
        _countDownTimer?.cancel();
        _postVideoDeduct(channelId.value, "1"); //用户加入后第一扣费
        _startTimer();
      },
      onUserOffline:
          (RtcConnection connection, int rUid, UserOfflineReasonType reason) {
        LogUtil.w(
            '[onUserOffline] connection: ${connection.toJson()}  rUid: $rUid reason: $reason');
        EasyLoading.showToast('The other party has hung up');
        _finishCall();
      },
      onLeaveChannel: (RtcConnection connection, RtcStats stats) {
        LogUtil.w(
            '[onLeaveChannel] connection: ${connection.toJson()} stats: ${stats.toJson()}');
        // _finishCall();
      },
      onRemoteVideoStateChanged: (RtcConnection connection, int remoteUid,
          RemoteVideoState state, RemoteVideoStateReason reason, int elapsed) {
        LogUtil.w(
            '[onRemoteVideoStateChanged] connection: ${connection.toJson()} remoteUid: $remoteUid state: $state reason: $reason elapsed: $elapsed');
        //TODO 出现一个现象 主播端秒挂的话 回调是 5用户离开频道， 3用户被服务器禁止
        if (state == RemoteVideoState.remoteVideoStateFailed) {
          EasyLoading.showToast('通话异常');
          _finishCall();
        }
      },
    );

    engine.registerEventHandler(_rtcEngineEventHandler);
    await engine.enableVideo();
    await engine.startPreview();
  }

  void _startTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _formatDuration(_secondsElapsed);
      _secondsElapsed++;
      //如果是匹配30s扣费一次，后面60s扣费一次
      var tmpSecond =
          (source == Constants.videoSourceMatch && _secondsElapsed > 30)
              ? _secondsElapsed - 30
              : _secondsElapsed;
      if (tmpSecond % 60 == 0) {
        // 每一分钟扣费一次
        _postVideoDeduct(channelId.value, "0");
      }
    });
  }

  _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      timeText.value =
          '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else if (minutes > 0) {
      timeText.value =
          '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      timeText.value = '00:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }

  _finishCall({bool isUserCancel = false}) {
    if (channelId.value.isNotEmpty) {
      if (joinUserId.value > 0) {
        //有用户加入频道 调用挂断
        _postVideoHangup(channelId.value);
      }
      //用户主动挂断才需要调用
      if (isUserCancel) {
        _postVideoPushCancel(channelId.value);
      }
    }
    _countDownTimer?.cancel();
    _countDownTimer = null;
    _callTimer?.cancel();
    _callTimer = null;
    manager.recalculateIntervalTime();
    manager.resumeLoop();
    Get.back();
  }

  Future<void> _postVideoDeduct(String channelId, String firstFlag) async {
    VideoDeductBean result = await HttpRequest.request<VideoDeductBean>(
        APIHub.anchor.postVideoDeduct,
        method: Method.POST,
        params: {
          'channelId': channelId,
          'firstFlag': firstFlag,
        },
        (p0) => VideoDeductBean.fromJson(p0),
        exception: (e) => {
          debugPrint('parse exception $e'),
          _finishCall()
        });
    if (result.enoughFlag == "1") {
      //余额充足
      userInfoService.user.value?.diamond = result.diamond;
      userInfoService.user.refresh();
    } else if (result.enoughFlag == "2") {
      //扣费失败 后台已经关闭频道
      EasyLoading.showToast("Insufficient diamonds, call ended");
      _finishCall();
    } else {
      //余额不足 提示充值
      userInfoService.user.value?.diamond = result.diamond;
      userInfoService.user.refresh();
    }
  }

  Future<void> _postVideoHangup(String channelId) async {
    dynamic result = await HttpRequest.request<dynamic>(
        APIHub.anchor.postVideoHangUp,
        method: Method.POST,
        queryData: {
          'channelId': channelId,
        },
        (p0) => ChannelBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});
  }

  Future<void> _postVideoPush(String channelId) async {
    dynamic result = await HttpRequest.request<dynamic>(
        APIHub.anchor.postVideoPush,
        method: Method.POST,
        queryData: {
          'channelId': channelId,
        },
        (p0) => ChannelBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});
  }

  Future<void> _postVideoPushCancel(String channelId) async {
    dynamic result = await HttpRequest.request<dynamic>(
        APIHub.anchor.postVideoCancel,
        method: Method.POST,
        queryData: {
          'channelId': channelId,
        },
        (p0) => ChannelBean.fromJson(p0),
        exception: (e) => {debugPrint('parse exception $e')});
  }

  Future<void> _switchCamera() async {
    await engine.switchCamera();
    switchCamera.value = !switchCamera.value;
  }

  _muteLocalVideoStream() async {
    await engine.muteLocalVideoStream(!muteCamera.value);
    muteCamera.value = !muteCamera.value;
  }

  void listenCall() {
    isIncomingCallListened.value = true;
    _genVideoChannel(anchorInfo.value.id!, false);
  }

  void showReportDialog() {}

  void showCloseDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomAlertDialog(
          title: "Hang up",
          content: "Girls are waiting for you. \nAre you sure to hang up now?",
          primaryButtonText: "Keep matching",
          onPrimaryButtonPressed: () {
            Navigator.of(context).pop();
          },
          secondaryButtonText: "hang up",
          onSecondaryButtonPressed: () {
            manager.addCancelCount();
            Navigator.of(context).pop();
            _finishCall(isUserCancel: true);
          },
        );
      },
    );
  }

  // 启动倒计时
  _startCountdown() {
    _countDownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds > 0) {
        _seconds--;
      } else {
        // 倒计时结束后的回调
        EasyLoading.showToast('无人接听');
        _finishCall();
        _countDownTimer?.cancel(); // 取消计时器
      }
    });
  }
}
