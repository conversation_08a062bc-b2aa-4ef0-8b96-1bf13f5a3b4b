import 'package:Chamatch/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../model/user_model.dart';
import '../../../service/UserInfoService.dart';
import '../../../storage/storage.dart';
import '../../../utils/Hive_util.dart';

class SplashController extends GetxController {
  UserInfoService userService = Get.find<UserInfoService>();

  @override
  Future<void> onReady() async {
    super.onReady();
    await _requestPermissions();
  }

  // 同时请求多个权限
  Future<void> _requestPermissions() async {
    // 请求多个权限，包括相机和相册权限
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Permission.microphone,
    ].request();

    // 判断每个权限的状态
    statuses.forEach((permission, status) {
      if (status.isGranted) {
        // 权限被授权
        String? token = HiveUtil.get<String>(StorageKeys.TOKEN_KEY);
        if (token == null) {
          Get.offAndToNamed('/login');
        } else {
          UserBean? userInfo = HiveUtil.get<UserBean>(StorageKeys.USER_KEY,
              fromJson: UserBean.fromJson);
          if (userInfo != null) {
            userService.setUser(userInfo);
            Get.offAndToNamed('/main-page');
          } else {
            Get.offAndToNamed('/login');
          }
        }
      } else if (status.isDenied) {
        // 权限被拒绝
        EasyLoading.showToast('${permission.toString()} permission denied');
      } else if (status.isPermanentlyDenied) {
        // 权限被永久拒绝
        EasyLoading.showToast('${permission.toString()} permission permanently denied');
        // 可以引导用户去设置页面开启权限
        openAppSettings();
      }
    });
  }
}
