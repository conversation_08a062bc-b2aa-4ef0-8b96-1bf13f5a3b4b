import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../http/http_request.dart';
import '../../../model/page_model.dart';
import '../../../model/user_model.dart';
import '../../../model/video_history_model.dart';
import '../../../repo/api_hub.dart';

class CallHistoryController extends GetxController {
  final videoHistoryList = <VideoHistoryBean>[].obs;
  var loadPage = 1;

  @override
  void onInit() {
    super.onInit();
    getCallHistory();
  }

  Future<void> getCallHistory() async {
    PageBean<VideoHistoryBean> pageBean = await HttpRequest.request(
        APIHub.user.videoHistory,
        method: Method.POST,
        params: {"current": "$loadPage", "size": "20"},
        (p0) =>
            PageBean<VideoHistoryBean>.fromJson(p0, VideoHistoryBean.fromJson),
        exception: (e) => {
              debugPrint('parse exception $e'),
              loadPage--,
              if (loadPage < 1) loadPage = 1
            });

    if (loadPage == 1) {
      videoHistoryList.assignAll(pageBean.records);
    } else {
      videoHistoryList.addAll(pageBean.records);
    }
    if (pageBean.records.isEmpty) {
      loadPage--;
      if (loadPage < 1) loadPage = 1;
    }
    // anchorItems.refresh();
  }

  void gotoAnchorDetail(VideoHistoryBean item) {
    UserBean argUserBean = UserBean.empty();
    argUserBean.id = item.anchorUserId;
    argUserBean.headFileName = item.headFileName;
    argUserBean.nickName = item.nickName;
    Get.toNamed('/profile', arguments: argUserBean);
  }

  String getCallStatus(VideoHistoryBean item) {
    if (item.videoStatus == '1') {
      return 'In call';
    } else if (item.videoStatus == '2') {
      return 'Call completed';
    } else {
      return 'Not connected';
    }
  }

  String getCallIcon(VideoHistoryBean item) {
    if (item.videoStatus == '1' || item.videoStatus == '2') {
      return 'assets/ic_call_suc.png';
    } else {
      return 'assets/ic_call_failed.png';
    }
  }
}
