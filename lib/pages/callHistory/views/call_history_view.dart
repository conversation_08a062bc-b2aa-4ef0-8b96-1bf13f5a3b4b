import 'package:Chamatch/widget/refresh.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../model/video_history_model.dart';
import '../../../widget/CustomNetworkImage.dart';
import '../controllers/call_history_controller.dart';

class CallHistoryView extends GetView<CallHistoryController> {
  const CallHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    RefreshController refreshController =
        RefreshController(initialRefresh: true);

    return Scaffold(
      body: Refresh(
        child: Obx(() => ListView.builder(
              itemCount: controller.videoHistoryList.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    _buildPostItem(controller.videoHistoryList[index]),
                    const Divider(
                      color: Color(0x1AFFFFFF), // 横线分割
                      height: 0.5,
                    ),
                  ],
                );
              },
            )),
        onRefresh: () {
          controller.loadPage = 1;
          controller.getCallHistory();
        },
        onLoad: () {
          controller.loadPage++;
          controller.getCallHistory();
        },
      ),
    );
  }

  Widget _buildPostItem(VideoHistoryBean item) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
      child: Material(
        color: Colors.transparent, // 设置透明背景色，防止遮挡
        child: InkWell(
          onTap: () {
            controller.gotoAnchorDetail(item); // 延迟调用
          },
          child: Row(
            children: [
              Image.asset(
                controller.getCallIcon(item),
                width: 24,
                height: 24,
              ),
              const SizedBox(
                width: 12,
              ),
              // 头像
              CustomHeaderImage(
                imageUrl: item.headFileName ?? '',
                width: 50,
              ),
              const SizedBox(width: 12),
              // 用户名和最后一条消息标题
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.nickName ?? '',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      controller.getCallStatus(item),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0x7FFFFFFF),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // 消息时间和未读消息数
              Text(
                item.createTime ?? '',
                style: const TextStyle(
                  fontSize: 11,
                  color: Color(0x7FFFFFFF),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
