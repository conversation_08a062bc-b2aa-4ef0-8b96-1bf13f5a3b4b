import 'dart:ui';

import 'package:Chamatch/pages/home/<USER>/home_view.dart';
import 'package:Chamatch/pages/match/views/match_view.dart';
import 'package:Chamatch/pages/mine/views/mine_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../widget/theme_container.dart';
import '../../message/message_page.dart';
import '../controllers/main_page_controller.dart';

class MainPageView extends GetView<MainPageController> {
  const MainPageView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    // 将页面实例化一次，避免重复构建
    final pages = [
      const HomeView(),
      const MatchView(),
      const MessagePage(),
      const MineView(),
    ];

    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: true, // 禁止内容区域调整以避免底部被覆盖
      body: ThemeContainer(
        child: Stack(
          children: [
            Obx(() => SafeArea(
              // 确保内容不会被系统 UI 和 BottomNavigationBar 遮挡
              child: IndexedStack(
                index: controller.initialPage.value,
                children: pages.map((page) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 70.0), // 手动添加底部内边距
                    child: page,
                  );
                }).toList(),
              ),
            )),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Obx(() => ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                  blendMode: BlendMode.srcOver,
                  child: Container(
                    padding: const EdgeInsets.only(top: 10, bottom: 0),
                    color: Colors.white10,
                    child: BottomNavigationBar(
                      currentIndex: controller.initialPage.value,
                      backgroundColor: null,
                      elevation: 0,
                      type: BottomNavigationBarType.fixed,
                      selectedItemColor: const Color(0xff47FFDA),
                      unselectedItemColor: Colors.white,
                      onTap: (index) {
                        controller.initialPage.value = index;
                      },
                      items: const [
                        BottomNavigationBarItem(
                          icon: Image(
                            width: 28,
                            height: 28,
                            image:
                            AssetImage('assets/tab_home_normal.png'),
                          ),
                          activeIcon: Image(
                            width: 28,
                            height: 28,
                            image:
                            AssetImage('assets/tab_home_checked.png'),
                          ),
                          label: '',
                        ),
                        BottomNavigationBarItem(
                          icon: Image(
                            width: 28,
                            height: 28,
                            image:
                            AssetImage('assets/tab_match_normal.png'),
                          ),
                          activeIcon: Image(
                            width: 28,
                            height: 28,
                            image: AssetImage(
                                'assets/tab_match_checked.png'),
                          ),
                          label: '',
                        ),
                        BottomNavigationBarItem(
                          icon: Image(
                            width: 28,
                            height: 28,
                            image: AssetImage(
                                'assets/tab_message_normal.png'),
                          ),
                          activeIcon: Image(
                            width: 28,
                            height: 28,
                            image: AssetImage(
                                'assets/tab_message_checked.png'),
                          ),
                          label: '',
                        ),
                        BottomNavigationBarItem(
                          icon: Image(
                            width: 28,
                            height: 28,
                            image:
                            AssetImage('assets/tab_mine_normal.png'),
                          ),
                          activeIcon: Image(
                            width: 28,
                            height: 28,
                            image:
                            AssetImage('assets/tab_mine_checked.png'),
                          ),
                          label: '',
                        ),
                      ],
                    ),
                  ),
                ),
              )),
            ),
          ],
        ),
      ),
    );
  }
}

