import 'package:Chamatch/pages/callHistory/controllers/call_history_controller.dart';
import 'package:Chamatch/pages/conversationList/controllers/conversation_list_controller.dart';
import 'package:Chamatch/pages/match/controllers/match_controller.dart';
import 'package:Chamatch/pages/mine/sub/wallet/controllers/wallet_controller.dart';
import 'package:get/get.dart';

import '../../../controller/user_controller.dart';
import '../../home/<USER>/home_controller.dart';
import '../../mine/controllers/mine_controller.dart';
import '../controllers/main_page_controller.dart';

class MainPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MainPageController>(
      () => MainPageController(),
    );
    Get.lazyPut<UserController>(
      () => UserController(),
    );
    Get.lazyPut<HomeController>(
      () => HomeController(),
    );
    Get.lazyPut<MatchController>(
      () => MatchController(),
    );
    Get.lazyPut<MineController>(
      () => MineController(),
    );
    Get.lazyPut<ConversationListController>(
      () => ConversationListController(),
    );
    Get.lazyPut<CallHistoryController>(
      () => CallHistoryController(),
    );
    Get.lazyPut<WalletController>(
      () => WalletController(),
    );
  }
}
