import 'dart:ffi';
import 'dart:io';

import 'package:Chamatch/utils/constants.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import '../../../engine/event_bus.dart';
import '../../../http/http_request.dart';
import '../../../im/custom_message/mikchat_message.dart';
import '../../../im/im_engine.dart';
import '../../../manager/IncomingAnchorManager.dart';
import '../../../model/system_config_model.dart';
import '../../../model/user_model.dart';
import '../../../repo/api_hub.dart';
import '../../../service/UserInfoService.dart';
import '../../../storage/storage.dart';
import '../../../utils/Hive_util.dart';
import '../../../utils/log.dart';

class MainPageController extends GetxController {
  late final PageController controller;
  final initialPage = 0.obs;
  final manager = IncomingAnchorManager();
  final userInfoService = Get.find<UserInfoService>();

  @override
  void onInit() {
    super.onInit();
    controller = PageController(
      initialPage: initialPage.value,
    );
    _getSystemConfig();
    _configImEngine();
    _checkAndConnectRongYun();

    // test();
  }

  @override
  void onReady() {
    super.onReady();
    _startAutoAnchorCalling();
  }

  @override
  void onClose() {
    super.onClose();
    //将接受引擎置空
    IMEngineManager().engine?.onMessageReceived = null;
  }

  Future<void> _getSystemConfig() async {
    ConfigurationBean result = await HttpRequest.request<ConfigurationBean>(
        APIHub.config,
        method: Method.GET,
        (p0) => ConfigurationBean.fromJson(p0),
        exception: (e) => {LogUtil.e(e)});
    HiveUtil.save(StorageKeys.systemConfig, result);
  }

  void _startAutoAnchorCalling() {
    ever(userInfoService.user, (UserBean? user) {
      if (user != null) {
        LogUtil.i('Diamond updated to: ${user.diamond}');
        ConfigurationBean? configurationBean = HiveUtil.get<ConfigurationBean>(
            StorageKeys.systemConfig,
            fromJson: ConfigurationBean.fromJson);
        int anchorDefaultCallDiamond =
            int.tryParse(configurationBean?.anchorDefaultCallAmount ?? '0') ??
                Constants.videoDefaultPrice;
        if (user.diamond > anchorDefaultCallDiamond) {
          manager.startLoop(() {
            LogUtil.i('触发来电逻辑: ');
            _getIncomingAnchor();
          });

          Future.delayed(const Duration(milliseconds: 1000), () {
            bool doNotDisturb =
                HiveUtil.get<bool>(StorageKeys.doNotDisturb) ?? false;
            if (doNotDisturb) {
              manager.pauseLoop();
            }
          });
        } else {
          //用户没钱 或者 中途没钱了 关闭轮询
          manager.stopLoop();
        }
      }
    });
  }

  Future<void> _getIncomingAnchor() async {
    UserBean userBean = await HttpRequest.request<UserBean>(
        APIHub.anchor.incomingAnchor,
        method: Method.POST,
        (p0) => UserBean.fromJson(p0),
        exception: (e) => {LogUtil.e(e)});

    if (userBean.id?.isNotEmpty ?? false) {
      Get.toNamed('/video-chat',
          arguments: [userBean, false, true, Constants.videoSourceAIB]);
    }
  }

  void _configImEngine() {
    IMEngineManager().engine?.onConnectionStatusChanged =
        (RCIMIWConnectionStatus? status) {
      if (status == RCIMIWConnectionStatus.connected) {
        LogUtil.d("Rong yun connect suc");
      } else if (status == RCIMIWConnectionStatus.tokenIncorrect) {
        //TODO 重新获取融云token
        _getRongYunToken();
      }
    };

    IMEngineManager().engine?.onMessageReceived =
        (RCIMIWMessage? message, int? left, bool? offline, bool? hasPackage) {
      LogUtil.i("vvvvvvv111 $message");
      if (message is MikChatMessage) {
        MikChatMessage mikChatMessage = message;
        if (mikChatMessage.type == "4" || mikChatMessage.type == "5") {
          bus.emit(EventBus.eventVideoReject, message.channelId);
        } else if (mikChatMessage.type == "14") {
          //后台推送的视频
          if (mikChatMessage.content != null) {
            downloadVideo(
                mikChatMessage.targetId ?? '', mikChatMessage.content!);
          }
        } else {
          bus.emit(EventBus.eventReceiveMessage, message);
        }
      } else {
        bus.emit(EventBus.eventReceiveMessage, message);
      }
    };
  }

  void _checkAndConnectRongYun() {
    var rongyunToken = HiveUtil.get<String>(StorageKeys.RONGYUN_TOKEN_KEY);
    if (rongyunToken == null) {
      _getRongYunToken();
    } else {
      _realConnectRongYunIM(rongyunToken);
    }
  }

  Future<void> _getRongYunToken() async {
    String result = await HttpRequest.request<String>(
        APIHub.getRongYunToken,
        method: Method.POST,
        (p0) => p0,
        exception: (e) => {LogUtil.e(e)});
    if (result.isNotEmpty) {
      _realConnectRongYunIM(result);
    }
    HiveUtil.save(StorageKeys.RONGYUN_TOKEN_KEY, result);
  }

  Future<void> _realConnectRongYunIM(String rongYunToken) async {
    RCIMIWConnectCallback connectCallback =
        RCIMIWConnectCallback(onDatabaseOpened: (int? code) {
      //数据库打开的回调0 代表成功，非 0 代表出现异常
      LogUtil.e("connectCallback onDatabaseOpened: $code");
    }, onConnected: (int? code, String? userId) {
      //收到连接状态的回调code 0 代表成功，非 0 代表出现异常, userId 链接成功的用户 ID
      LogUtil.e("connectCallback onConnected: $code  $userId");
      if (code == 0) {
        // EasyLoading.showToast("连接成功");
      }
    });
    int? code = await IMEngineManager()
        .engine
        ?.connect(rongYunToken, 60, callback: connectCallback);
    LogUtil.e("connectRongYun: $code");
  }

  Future<void> downloadVideo(String targetId, String url) async {
    try {
      Directory directory = await getApplicationDocumentsDirectory();
      String filePath = "${directory.path}/downloaded_video.mp4";

      Dio dio = Dio();
      await dio.download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            LogUtil.e("vvvvvvv ${received / total}");
          }
        },
      );
      var userBean = UserBean.empty();
      userBean.id = targetId;
      Get.toNamed('/aia-video', arguments: [userBean, filePath]); // 下载完成跳转播放页面
    } catch (e) {
      EasyLoading.showToast("Download failed: $e");
    }
  }

  test() {
    downloadVideo("1837424868711780354",
        "https://bdckj.s3.ap-southeast-1.amazonaws.com/1724297235077");
  }
}
