import 'package:Chamatch/app/routes/app_pages.dart';
import 'package:Chamatch/im/custom_message/mikchat_ask_gift_message.dart';
import 'package:Chamatch/im/custom_message/mikchat_gift_message.dart';
import 'package:Chamatch/im/custom_message/mikchat_message.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:Chamatch/utils/DeviceInfo.dart';
import 'package:Chamatch/utils/Hive_util.dart';
import 'package:Chamatch/utils/VColor.dart';
import 'package:Chamatch/utils/inapp_purchase_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';

import 'im/im_engine.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
  );
  final ThemeData lightTheme = ThemeData.light().copyWith(
      textTheme: const TextTheme(
          titleMedium: TextStyle(color: Colors.white, fontSize: 16),
          titleSmall: TextStyle(color: Colors.white, fontSize: 14),
          bodyMedium: TextStyle(color: Colors.white, fontSize: 16),
          bodySmall: TextStyle(color: Colors.white, fontSize: 14)),
      appBarTheme: const AppBarTheme(
              centerTitle: true,
              iconTheme: IconThemeData(color: Colors.white),
              titleTextStyle:
                  TextStyle(color: Colors.white, fontSize: 18), //标题字体
              color: Colors.white)
          .copyWith(backgroundColor: Colors.transparent),
      canvasColor: Colors.transparent,
      primaryColor: VColor.colorPrimary,
      tabBarTheme: const TabBarThemeData(
          dividerColor: VColor.colorPrimary,
          labelColor: Color(0xffffffff),
          unselectedLabelColor: Color(0xff898989),
          indicatorColor: VColor.colorPrimary),
      highlightColor: const Color.fromRGBO(0, 0, 0, 0),
      splashColor: const Color.fromRGBO(0, 0, 0, 0),
      scaffoldBackgroundColor: Colors.transparent);
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await HiveUtil.init();
  await DeviceInfo.getDeviceId();
  await initRongYun();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  Get.put(UserInfoService());

  final ThemeData darkTheme = ThemeData.dark();

  // final inAppPurchaseService = InAppPurchaseService()..init();
  // await inAppPurchaseService.init();

  // 切换主题的方法
  void toggleTheme() =>
      Get.changeThemeMode(Get.isDarkMode ? ThemeMode.light : ThemeMode.dark);
  runApp(GetMaterialApp(
      title: "1v1 video chat",
      theme: lightTheme,
      darkTheme: darkTheme,
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.light,
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
      // home: const MainPageView(),
      builder: EasyLoading.init()));
}

Future<void> initRongYun() async {
  if (IMEngineManager().engine == null) {
    RCIMIWEngineOptions options = RCIMIWEngineOptions.create();
    options.areaCode = RCIMIWAreaCode.sg;
    RCIMIWEngine engine = await RCIMIWEngine.create("pgyu6atqpbvpu", options);
    engine.registerCustomMessage('mikchat', (json) {
      MikChatMessage mikChatMsg = MikChatMessage.fromJson(json);
      // 此处 'content' 不可修改
      mikChatMsg.decode(json['content']);
      return mikChatMsg;
    });
    engine.registerCustomMessage('mikchat:askgift', (json) {
      MikChatAskGiftMessage mikChatAskGiftMsg =
          MikChatAskGiftMessage.fromJson(json);
      // 此处 'content' 不可修改
      mikChatAskGiftMsg.decode(json['content']);
      return mikChatAskGiftMsg;
    });
    engine.registerCustomMessage('mikchat:gift', (json) {
      MikChatGiftMessage mikChatGiftMsg = MikChatGiftMessage.fromJson(json);
      // 此处 'content' 不可修改
      mikChatGiftMsg.decode(json['content']);
      return mikChatGiftMsg;
    });

    IMEngineManager().engine = engine;
  }
}
