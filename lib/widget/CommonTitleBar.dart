import 'package:flutter/material.dart';

class CommonTitleBar extends StatelessWidget {
  final String title;
  final Color? titleColor;
  final IconData? leftIcon;
  final List<String>? rightIcons;
  final VoidCallback? onLeftPressed;
  final List<VoidCallback>? onRightPressed;

  CommonTitleBar({
    required this.title,
    this.titleColor,
    this.leftIcon,
    this.rightIcons,
    this.onLeftPressed,
    this.onRightPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      color: titleColor ?? Colors.black.withOpacity(0),
      child: Row(
        children: [
          if (leftIcon != null)
            IconButton(
              icon: Icon(leftIcon, color: Colors.white),
              onPressed: onLeftPressed,
            ),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.start,
            ),
          ),
          if (rightIcons != null && rightIcons!.isNotEmpty)
            Row(
              children: rightIcons!.asMap().entries.map((entry) {
                int index = entry.key;
                String icon = entry.value;
                return IconButton(
                  icon:
                  Image.asset(icon, width: 24, height: 24),
                  onPressed: onRightPressed != null && onRightPressed!.length > index
                      ? onRightPressed![index]
                      : null,
                );
              }).toList(),
            ),
        ],
      ),
    );
  }
}
