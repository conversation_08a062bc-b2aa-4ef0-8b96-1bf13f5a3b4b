import 'package:Chamatch/model/recharge_model.dart';
import 'package:flutter/material.dart';

typedef OnItemTapCallback = void Function(RechargeBean bean);

class ItemRechargeWidget extends StatefulWidget {
  final RechargeBean bean;
  final OnItemTapCallback itemCallback;

  const ItemRechargeWidget(
      {super.key, required this.bean, required this.itemCallback});

  @override
  State<StatefulWidget> createState() => _ItemRechargeWidgetState();
}

class _ItemRechargeWidgetState extends State<ItemRechargeWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.transparent,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Image.asset(
                        'assets/ic_diamond.png', // 小钻石图片
                        width: 50,
                        height: 50,
                      ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.bean.itemName}',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (widget.bean.level?.isNotEmpty == true)
                        Text(
                          'LV${widget.bean.level} Bonus: ${widget.bean.giveNum}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  widget.itemCallback(widget.bean);
                },
                child: Container(
                  height: 35,
                  width: 90,
                  decoration: BoxDecoration(
                    color: const Color(0xFF10DA23),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '${widget.bean.currency}${widget.bean.price}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.bean.message?.isNotEmpty == true)
          Positioned(
            top: -10,
            child: Stack(
              children: [
                Image.asset(
                  "assets/ic_recharge_label.png",
                  height: 20,
                  fit: BoxFit.fitHeight,
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                  child: Text(
                    '${widget.bean.message}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
          ),
      ],
    );
  }
}
