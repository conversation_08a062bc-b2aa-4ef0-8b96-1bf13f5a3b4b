import 'package:flutter/cupertino.dart';

class GlowingBorderPainter extends CustomPainter {
  final Color color;

  GlowingBorderPainter({this.color = const Color(0xff7aad85)});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeWidth = 1
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 5); // 添加发光效果

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRRect(RRect.fromRectAndRadius(rect, const Radius.circular(1)), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
