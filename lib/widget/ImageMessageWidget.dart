import 'dart:convert';  // 导入用于Base64解码的库
import 'dart:typed_data';  // 导入Uint8List
import 'package:flutter/material.dart';

class ImageMessageWidget extends StatelessWidget {
  final String base64String;

  const ImageMessageWidget({super.key, required this.base64String});

  @override
  Widget build(BuildContext context) {
    // 解码 Base64 字符串为 Uint8List
    Uint8List bytes = base64Decode(base64String);

    return ClipRRect(
      borderRadius: BorderRadius.circular(16),  // 设置圆角为16
      child: Image.memory(
        bytes,  // 使用解码后的字节数据
        width: 100,   // 设置宽度为100
        height: 100,  // 设置高度为200
        fit: BoxFit.cover,  // 设置显示方式
      ),
    );
  }
}
