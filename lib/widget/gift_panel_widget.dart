import 'package:Chamatch/controller/user_controller.dart';
import 'package:Chamatch/model/gift_model.dart';
import 'package:Chamatch/model/user_model.dart';
import 'package:Chamatch/service/UserInfoService.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../manager/gift_manager.dart';

class GiftPanelWidget extends StatefulWidget {
  final OnSendCallback callback;

  const GiftPanelWidget({super.key, required this.callback});

  @override
  State<GiftPanelWidget> createState() => _GiftPanelWidgetState();
}

class _GiftPanelWidgetState extends State<GiftPanelWidget> {
  UserBean? bean = Get.find<UserInfoService>().user.value;
  var controller = Get.find<UserController>();
  List<GiftBean> gifts = [];
  GiftBean? selectedGift; // 用于跟踪选中的礼物
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchGifts();
  }

  _fetchGifts() async {
    _isLoading = true;
    gifts = (await controller.getGiftList()).first.giftsList;
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    int itemsPerPage = 8; // 每页显示的礼物数量
    int totalPages = (gifts.length / itemsPerPage).ceil(); // 总页数
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            Container(
              height: 360,
              padding: const EdgeInsets.only(
                  left: 16, top: 20, right: 16, bottom: 30),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: PageView.builder(
                      itemCount: totalPages,
                      scrollDirection: Axis.horizontal,
                      physics: const PageScrollPhysics(),
                      itemBuilder: (context, pageIndex) {
                        // 计算当前页的礼物数据
                        final pageGifts = gifts
                            .skip(pageIndex * itemsPerPage)
                            .take(itemsPerPage)
                            .toList();
                        return GridView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4, // 每行4个
                            mainAxisSpacing: 25,
                            crossAxisSpacing: 16,
                            childAspectRatio: 0.8,
                          ),
                          itemCount: pageGifts.length,
                          itemBuilder: (context, index) {
                            final gift = pageGifts[index];
                            final isSelected = gift == selectedGift; // 检查是否选中
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedGift = gift; // 更新选中状态
                                });
                              },
                              child: Column(
                                children: [
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        color: isSelected
                                            ? const Color(0xff12DB2B)
                                                .withOpacity(0.1)
                                            : Colors.transparent,
                                        border: isSelected
                                            ? Border.all(
                                                color: const Color(0xff12DB2B),
                                                width: 2.0,
                                              )
                                            : Border.all(
                                                color: Colors.transparent,
                                                width: 2.0,
                                              ),
                                      ),
                                      child: Center(
                                          child: Image.network(
                                        "${gift.giftIcon}",
                                        width: 54,
                                        fit: BoxFit.contain,
                                      )),
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    "💎 ${gift.giftPrice} ",
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 14),
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          "💎 ${bean?.diamond}",
                          style: const TextStyle(
                              color: Colors.white, fontSize: 16),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          // 处理发送礼物
                          if (selectedGift != null) {
                            widget.callback.call(selectedGift!);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 5),
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(colors: [
                                Color(0xff10DB21),
                                Color(0xff23E0BA),
                              ]),
                              borderRadius: BorderRadius.circular(50)),
                          child: const Text(
                            "Send",
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (_isLoading)
              const Positioned.fill(
                  child: Align(
                      alignment: Alignment.center, // 居中对齐
                      child: SizedBox(
                        width: 100,
                        height: 100,
                        child: RefreshProgressIndicator(
                          color: Color(0xff12DB2B),
                        ),
                      )))
          ],
        )
      ],
    );
  }
}
