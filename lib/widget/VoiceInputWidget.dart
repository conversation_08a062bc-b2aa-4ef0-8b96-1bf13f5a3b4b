import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class VoiceInputWidget extends StatefulWidget {
  final Function(String filePath) onSendAudio;

  const VoiceInputWidget({Key? key, required this.onSendAudio}) : super(key: key);

  @override
  _VoiceInputWidgetState createState() => _VoiceInputWidgetState();
}

class _VoiceInputWidgetState extends State<VoiceInputWidget> {
  late FlutterSoundRecorder _recorder;
  bool _isRecording = false;
  bool _isCanceled = false;
  double _dbLevel = 0.0;
  String? _filePath;
  DateTime? _startTime;

  @override
  void initState() {
    super.initState();
    _recorder = FlutterSoundRecorder();
    _initializeRecorder();
  }

  Future<void> _initializeRecorder() async {
    await _recorder.openRecorder();
    await _recorder.setSubscriptionDuration(const Duration(milliseconds: 100));
  }

  @override
  void dispose() {
    _recorder.closeRecorder();
    _recorder = null!;
    super.dispose();
  }

  Future<void> _startRecording() async {
    Directory tempDir = await getTemporaryDirectory();
    _filePath = '${tempDir.path}/recorded_audio.aac';

    await _recorder.startRecorder(toFile: _filePath, codec: Codec.aacADTS);
    _startTime = DateTime.now();
    _isRecording = true;

    _recorder.onProgress?.listen((event) {
      setState(() {
        _dbLevel = event.decibels ?? 0.0;
      });
    });
  }

  Future<void> _stopRecording() async {
    await _recorder.stopRecorder();
    _isRecording = false;

    if (!_isCanceled) {
      final duration = DateTime.now().difference(_startTime!);
      if (duration.inSeconds < 1) {
        _showMessage('Recording too short');
        return;
      }

      // 调用发送回调
      widget.onSendAudio(_filePath!);
    }
  }

  void _cancelRecording() {
    _isCanceled = true;
    _stopRecording();
    _showMessage('Recording canceled');
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (_isRecording)
          Container(
            color: Colors.black.withOpacity(0.7),
            height: 200,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.mic, size: 50, color: Colors.red.withOpacity(_dbLevel / 120)),
                Text(
                  'Recording...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        GestureDetector(
          onLongPressStart: (details) {
            _isCanceled = false;
            _startRecording();
          },
          onLongPressMoveUpdate: (details) {
            if (details.localPosition.dy < -50) {
              _isCanceled = true;
            }
          },
          onLongPressEnd: (details) {
            if (_isCanceled) {
              _cancelRecording();
            } else {
              _stopRecording();
            }
          },
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.blue,
            ),
            child: Icon(Icons.mic, color: Colors.white, size: 30),
          ),
        ),
      ],
    );
  }
}
