import 'package:Chamatch/utils/VColor.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';

class Refresh extends StatefulWidget {
  final Widget child;
  final Function onRefresh;
  final Function onLoad;

  const Refresh(
      {super.key,
      required this.child,
      required this.onRefresh,
      required this.onLoad});

  @override
  State<StatefulWidget> createState() => _RefreshState();
}

class _RefreshState extends State<Refresh> {
  late EasyRefreshController _controller;
  int _count = 10;
  Axis _scrollDirection = Axis.vertical;
  final _MIProperties _headerProperties = _MIProperties(
    name: 'Header',
  );
  final _MIProperties _footerProperties = _MIProperties(
    name: 'Footer',
  );

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return EasyRefresh(
      clipBehavior: Clip.none,
      controller: _controller,
      refreshOnStart:true,
      header: MaterialHeader(
        clamping: _headerProperties.clamping,
        showBezierBackground: _headerProperties.background,
        bezierBackgroundAnimation: _headerProperties.animation,
        bezierBackgroundBounce: _headerProperties.bounce,
        infiniteOffset: _headerProperties.infinite ? 100 : null,
        springRebound: _headerProperties.listSpring,
        color: VColor.colorPrimary,
        backgroundColor: VColor.black,
      ),
      footer: MaterialFooter(
        clamping: false,
        showBezierBackground: _footerProperties.background,
        bezierBackgroundAnimation: _footerProperties.animation,
        bezierBackgroundBounce: _footerProperties.bounce,
        infiniteOffset: _footerProperties.infinite ? 100 : null,
        springRebound: _footerProperties.listSpring,
        color: VColor.colorPrimary,
        backgroundColor: VColor.black,
      ),
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
        if (!mounted) {
          return;
        }
        widget.onRefresh.call();
        _controller.finishRefresh();
        _controller.resetFooter();
      },
      onLoad: () async {
        await Future.delayed(const Duration(seconds: 1));
        if (!mounted) {
          return;
        }
        widget.onLoad.call();
        _controller.finishLoad(
            _count >= 20 ? IndicatorResult.noMore : IndicatorResult.success);
      },
      child: widget.child,
    );
  }
}

class _MIProperties {
  final String name;
  bool clamping = true;
  bool background = false;
  bool animation = false;
  bool bounce = false;
  bool infinite = false;
  bool listSpring = false;

  _MIProperties({
    required this.name,
  });
}
