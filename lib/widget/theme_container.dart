import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ThemeContainer extends StatefulWidget {
  final Widget child;

  const ThemeContainer({super.key, required this.child});

  @override
  State<StatefulWidget> createState() => _ThemeContainer();
}

class _ThemeContainer extends State<ThemeContainer> {
  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF141414),
              Color(0xFF141414),
              Color(0xFF141414),
            ],
          ),
        ),
        child: widget.child);
  }
}
