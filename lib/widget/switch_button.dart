import 'package:flutter/material.dart';

class SwitchButton extends StatefulWidget {
  bool? value;
  ValueChanged<bool>? onChanged;

  SwitchButton({super.key, required this.value, required this.onChanged});

  @override
  State<StatefulWidget> createState() => _SwitchButtonState();
}

class _SwitchButtonState extends State<SwitchButton> {
  static final List<Color> inactiveColors = [const Color(0xff344946), const Color(0xff344949)];
  static final List<Color> activeColors = [const Color(0xff55b385), const Color(0xffb3e8ad)];

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
            onTap: () {
              setState(() {
                widget.value = true;
              });
              widget.onChanged?.call(true);
            },
            child: Container(
              height: 30,
              width: 50,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  gradient: LinearGradient(colors: widget.value == true ? activeColors : inactiveColors),
                  // color: Colors.white30,
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(45), bottomLeft: Radius.circular(45))),
              child: Text(
                'ON',
                textAlign: TextAlign.center,
                style: TextStyle(height: 1, color: widget.value == true ? Colors.black : Colors.white),
              ),
            )),
        InkWell(
            onTap: () {
              setState(() {
                widget.value = false;
              });
              widget.onChanged?.call(false);
            },
            child: Container(
                height: 30,
                width: 50,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    gradient: LinearGradient(colors: widget.value == false ? activeColors : inactiveColors),
                    borderRadius: const BorderRadius.only(topRight: Radius.circular(45), bottomRight: Radius.circular(45))),
                child: Text(
                  'OFF',
                  textAlign: TextAlign.center,
                  style: TextStyle(height: 1, color: widget.value == false ? Colors.black : Colors.white),
                )))
      ],
    );
  }
}
