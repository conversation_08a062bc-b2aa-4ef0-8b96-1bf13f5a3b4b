import 'package:Chamatch/widget/CommonTitleBar.dart';
import 'package:Chamatch/widget/theme_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ThemeScaffold extends StatefulWidget {
  final String? title;
  final Color? titleColor;
  final IconData? leftIcon;
  final List<String>? rightIcons;
  final VoidCallback? onLeftPressed;
  final List<VoidCallback>? onRightPressed;
  final Widget body;
  final List<Widget>? actions;
  final PreferredSizeWidget? appBarBottom;

  const ThemeScaffold(
      {super.key,
      this.title,
      required this.body,
      this.actions,
      this.appBarBottom,
      this.titleColor,
      this.leftIcon,
      this.rightIcons,
      this.onLeftPressed,
      this.onRightPressed});

  @override
  State<StatefulWidget> createState() => _ThemeScaffold();
}

class _ThemeScaffold extends State<ThemeScaffold> {
  @override
  Widget build(BuildContext context) {
    return ThemeContainer(
        child: Scaffold(
      appBar: AppBar(
        title: CommonTitleBar(
          title: widget.title ?? "",
          titleColor: widget.titleColor,
          leftIcon: widget.leftIcon,
          rightIcons: widget.rightIcons,
          onLeftPressed: widget.onLeftPressed,
          onRightPressed: widget.onRightPressed,
        ),
        actions: widget.actions,
        bottom: widget.appBarBottom,
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.transparent,
        ),
      ),
      body: widget.body,
    ));
  }
}
