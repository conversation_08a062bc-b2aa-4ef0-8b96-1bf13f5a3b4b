import 'package:flutter/widgets.dart';

class MatchRadarWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _MatchRadarWidgetState();
}

class _MatchRadarWidgetState extends State<MatchRadarWidget>
    with TickerProviderStateMixin {
  late AnimationController _radarController;
  late Animation<double> _radarAnimation;

  @override
  void initState() {
    super.initState();

    // 雷达动画
    _radarController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _radarAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _radarController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _radarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _radarAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: RadarPainter(progress: _radarAnimation.value),
        );
      },
    );
  }
}

class RadarPainter extends CustomPainter {
  final double progress;

  RadarPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    Paint gradientPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          Color(0xFF10DB21).withOpacity(0.1),
          Color(0xFF23E0B9).withOpacity(0.3),
          Color(0xFF23E0B9).withOpacity(0.5),
        ],
        stops: [0.3, 0.7, 1.0],
      ).createShader(Rect.fromCircle(
          center: Offset(size.width / 2, size.height / 2),
          radius: size.width / 2));

    canvas.drawCircle(Offset(size.width / 2, size.height / 2),
        size.width * progress, gradientPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
