import 'dart:math';

import 'package:Chamatch/model/pair.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class FakeAnimationAnchorWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _AvatarCircleState();
}

class _AvatarCircleState extends State<FakeAnimationAnchorWidget>
    with TickerProviderStateMixin {
  List<Pair> avatars = [
    const Pair("Vlisa🦋", "assets/random_header1.png"),
    const Pair("Raysa", "assets/random_header2.png"),
    const Pair("Kiyaa", "assets/random_header3.png"),
    const Pair("Maurin", "assets/random_header4.png"),
    const Pair("nazwa", "assets/random_header5.png"),
    const Pair("Laura_", "assets/random_header6.png"),
  ];

  double radius = 150; // 环形头像的半径
  double avatarRadius = 40; // 每个头像的半径
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  double rotationOffset = pi / 6;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(avatars.length, (index) {
      // 为每个头像创建独立的 AnimationController
      return AnimationController(
        vsync: this,
        duration: Duration(seconds: 2 + Random().nextInt(3)), // 随机动画持续时间
      )..repeat(reverse: true); // 循环并反转动画
    });

    _animations = List.generate(avatars.length, (index) {
      // 为每个头像创建独立的缩放动画，缩放范围 0.8 到 1.2
      double scaleBegin = 0.8 + Random().nextDouble() * 0.4; // 随机范围 0.8 - 1.2
      return Tween<double>(
              begin: scaleBegin, end: scaleBegin + Random().nextDouble() * 0.4)
          .animate(
        CurvedAnimation(parent: _controllers[index], curve: Curves.easeInOut),
      );
    });
  }

  @override
  void dispose() {
    // 清理所有 AnimationController
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 2 * (radius + avatarRadius), // 确保Stack足够大容纳所有头像
      height: 2 * (radius + avatarRadius), // 同上
      child: Stack(
        alignment: Alignment.center, // 确保头像环相对于Stack居中
        clipBehavior: Clip.none, // 防止头像被裁剪
        children: List.generate(avatars.length, (index) {
          double angle = (index / avatars.length) * 2 * pi + rotationOffset;
          double x = radius * cos(angle); // 计算x坐标
          double y = radius * sin(angle); // 计算y坐标

          return Positioned(
            left: x + radius, // 头像偏移量 + radius，确保头像在中间
            top: y + radius, // 同上
            child:
                // 使用AnimatedBuilder实现独立缩放动画
                AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return Transform.scale(
                  scale: _animations[index].value,
                  child: child,
                );
              },
              child: Column(children: [
                CircleAvatar(
                  radius: avatarRadius, // 设置头像的半径
                  backgroundImage: AssetImage(avatars[index].second),
                ),
                const SizedBox(height: 5),
                Text(
                  '${avatars[index].first}',
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                )
              ]),
            ),
          );
        }),
      ),
    );
  }
}
