import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class CustomNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final String placeholderImage;
  final String errorImage;
  final double borderRadius;

  const CustomNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholderImage = 'assets/ic_default_image.png',
    this.errorImage = 'assets/ic_default_image.png',
    this.borderRadius = 8.0, // 默认圆角为8
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius), // 设置圆角
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => Image.asset(
          placeholderImage,
          width: width,
          height: height,
          fit: fit,
        ), // 占位图
        errorWidget: (context, url, error) => Image.asset(
          errorImage,
          width: width,
          height: height,
          fit: fit,
        ), // 错误图
      ),
    );
  }
}

class CustomHeaderImage extends StatelessWidget {
  final String imageUrl;
  final double width;
  final BoxFit fit;
  final String placeholderImage;
  final String errorImage;

  const CustomHeaderImage({
    Key? key,
    required this.imageUrl,
    required this.width,
    this.fit = BoxFit.cover,
    this.placeholderImage = 'assets/ic_defalut_header.png',
    this.errorImage = 'assets/ic_defalut_header.png',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(width/2), // 设置圆角
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: width,
        fit: fit,
        placeholder: (context, url) => Image.asset(
          placeholderImage,
          width: width,
          height: width,
          fit: fit,
        ), // 占位图
        errorWidget: (context, url, error) => Image.asset(
          errorImage,
          width: width,
          height: width,
          fit: fit,
        ), // 错误图
      ),
    );
  }
}
