import 'package:Chamatch/model/recharge_model.dart';
import 'package:Chamatch/pages/mine/sub/wallet/controllers/wallet_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'item_recharge_widget.dart';

class ItemPaymentMethodWidget extends StatefulWidget {
  final RechargeBean bean;
  final String? localizedPrice;
  final OnItemTapCallback itemCallback;

  const ItemPaymentMethodWidget(
      {super.key,
      required this.bean,
      required this.localizedPrice,
      required this.itemCallback});

  @override
  State<StatefulWidget> createState() => _ItemPaymentMethodWidgetState();
}

class _ItemPaymentMethodWidgetState extends State<ItemPaymentMethodWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: const Color(0xFF101010),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.transparent,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.network(
                    "${widget.bean.imageUrl}",
                    width: 50,
                    height: 30,
                    fit: BoxFit.cover,
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.bean.payTypeMessage}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  widget.itemCallback(widget.bean);
                },
                child: Container(
                  height: 35,
                  width: 90,
                  decoration: BoxDecoration(
                    color: const Color(0xFF10DA23),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    widget.localizedPrice ?? "${widget.bean.currency}${widget.bean.price}",
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
