import 'package:flutter/material.dart';

import 'GradientButton.dart';

class CustomAlertDialog extends StatelessWidget {
  final String title; // 弹窗标题
  final String content; // 弹窗内容
  final String primaryButtonText; // 高亮按钮文字
  final VoidCallback? onPrimaryButtonPressed; // 高亮按钮点击事件
  final String secondaryButtonText; // 普通按钮文字
  final VoidCallback? onSecondaryButtonPressed; // 普通按钮点击事件

  const CustomAlertDialog({
    Key? key,
    required this.title,
    required this.content,
    required this.primaryButtonText,
    required this.onPrimaryButtonPressed,
    required this.secondaryButtonText,
    required this.onSecondaryButtonPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      content: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF145B0E), Color(0xFF0F0F0F)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // 内容
            Text(
              content,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFF9F9CA6),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            // 高亮按钮
            GradientButton(
              text: primaryButtonText,
              onPressed: () {
                onPrimaryButtonPressed!();
              },
              width: 260,
              // 可选参数
              height: 44,
              // 可选参数
              borderRadius: 22.0, // 可选参数
            ),
            const SizedBox(height: 8),
            // 普通按钮
            TextButton(
              onPressed: onSecondaryButtonPressed,
              child: Text(
                secondaryButtonText,
                style: const TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
