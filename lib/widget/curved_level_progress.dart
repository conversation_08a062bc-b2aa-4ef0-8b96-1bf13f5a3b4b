import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'dart:math' as math;

class CurvedLevelProgress extends StatefulWidget {
  const CurvedLevelProgress({super.key});

  @override
  State<CurvedLevelProgress> createState() => _CurvedLevelProgressState();
}

class _CurvedLevelProgressState extends State<CurvedLevelProgress> {
  int currentLevel = 0;

  final List<Map<String, dynamic>> levels = [
    {"label": "Lv0-10", "value": 10},
    {"label": "Lv11-20", "value": 20},
    {"label": "Lv21-30", "value": 30},
    {"label": "Lv31-40", "value": 40},
    {"label": "Lv41-50", "value": 50},
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      height: 120,
      child: CustomPaint(
        size: Size(MediaQuery.of(context).size.width, 100),
        painter: SingleCurveProgressPainter(
          progress: currentLevel / 50,
          points: List.generate(levels.length, (index) => index / (levels.length - 1)),
          activeLevel: currentLevel,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: levels.map((level) {
            bool isActive = level["value"] <= currentLevel;
            bool isCurrent = level["value"] == currentLevel;

            return GestureDetector(
              onTap: () => setState(() => currentLevel = level["value"]),
              child: Column(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    alignment: Alignment.center,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        if (isCurrent)
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.3),
                            ),
                          ),
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isActive ? Colors.white : Colors.grey[400],
                            boxShadow: isCurrent ? [
                              BoxShadow(
                                color: Colors.white.withOpacity(0.5),
                                blurRadius: 6,
                                spreadRadius: 2,
                              )
                            ] : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    level["label"],
                    style: TextStyle(
                      fontSize: 12,
                      color: isActive ? Colors.white : Colors.grey[400],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

class SingleCurveProgressPainter extends CustomPainter {
  final double progress;
  final List<double> points;
  final int activeLevel;

  SingleCurveProgressPainter({
    required this.progress,
    required this.points,
    required this.activeLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height * 1.5);
    final arcHeight = size.height * 0.15; // 控制弧度高度

    // 背景路径
    final backgroundPath = Path();

    // 创建一个整体的弧形路径
    backgroundPath.moveTo(0, size.height * 0.4);
    backgroundPath.quadraticBezierTo(
      size.width / 2,  // 控制点 x
      size.height * 0.1 - arcHeight, // 控制点 y，向上偏移以创建弧度
      size.width,  // 终点 x
      size.height * 0.4, // 终点 y
    );

    // 绘制背景路径
    final backgroundPaint = Paint()
      ..color = Colors.grey[600]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawPath(backgroundPath, backgroundPaint);

    // 计算活跃进度终点
    final activeEndX = size.width * (activeLevel / 50);

    // 创建裁剪路径用于活跃部分
    final activePath = Path();
    activePath.moveTo(0, size.height * 0.4);
    activePath.quadraticBezierTo(
      size.width / 2,
      size.height * 0.1 - arcHeight,
      activeEndX,
      size.height * 0.4 - math.sin(math.pi * (activeEndX / size.width)) * arcHeight,
    );

    // 绘制活跃路径
    final activePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawPath(activePath, activePaint);

    // 绘制点
    for (var point in points) {
      final x = point * size.width;
      final y = size.height * 0.1 - math.sin(math.pi * point) * arcHeight;

      // 可以在这里添加点的绘制，如果需要的话
    }
  }

  @override
  bool shouldRepaint(SingleCurveProgressPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.activeLevel != activeLevel;
  }
}
