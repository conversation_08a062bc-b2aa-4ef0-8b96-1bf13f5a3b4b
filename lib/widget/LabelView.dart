import 'package:flutter/material.dart';

class LabelView extends StatelessWidget {
  final String label;
  final int value;

  const LabelView({
    Key? key,
    required this.label,
    required this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0x1AFFFFFF),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 4),
          Text(
            '+$value',
            style: const TextStyle(fontSize: 14, color:Colors.white10, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
