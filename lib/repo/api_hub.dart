class APIHub {
  static String config = "/blade-auth/auth/sys/param";

  static String getRongYunToken = "/ks-mikchat/message/rongcloud/token";
  static _User user = _User();
  static _Anchor anchor = _Anchor();
  static _Order order = _Order();
}

class _User {
  String login = "/blade-auth/auth/token";
  String userDetail = "/blade-auth/user/detail";
  String userGift = "/ks-mikchat/gift/income/list";
  String followFlag = "/blade-auth/user/follow/flag";
  String videoHistory = "/ks-mikchat/video/history/list/mind";
  String giftList = "/ks-mikchat/gift/list";
  String uploadFile = "/materesource/resource/upload";
  String countryList = "/blade-auth/auth/country/list";
  String updateProfile = "/blade-auth/user/update";
  String diamondHistory = "/blade-auth/diamond/record/page";
}

class _Anchor {
  String recommendList = "/ks-mikchat/anchor/recommend";
  String popularList = "/ks-mikchat/anchor/popular";
  String newList = "/ks-mikchat/anchor/new";
  String followList = "/ks-mikchat/anchor/follow";

  String anchorDetail = "/ks-mikchat/anchor/detail";
  String anchorlitDetail = "/ks-mikchat/anchor/litDetail";
  String randomAnchor = "/ks-mikchat/video/random/anchor";

  String genVideoChannel = "/ks-mikchat/video/channel";
  String getVideoRtcToken = "/ks-mikchat/video/rtc/token";
  String postVideoHangUp = "/ks-mikchat/video/hangup";
  String postVideoCancel = "/ks-mikchat/video/push/cancel";
  String postVideoDeduct = "/ks-mikchat/video/deduct";
  String postVideoPush = "/ks-mikchat/video/push";
  String incomingAnchor = "/ks-mikchat/video/incoming/anchor";
  String giftive = "/ks-mikchat/gift/give";
  String followFlag = "/blade-auth/user/follow/flag";
  String follow = "/blade-auth/user/follow/mind";
  String unFollow = "/blade-auth/user/unfollow/mind";
}

class _Order {
  String rechargeList = "/ks-order/order/price";
  String paymentList = "/ks-order/order/price/paymethod";
  String createOrder = "/ks-order/order/save/new";
  String checkOrder = "/ks-order/order/pay/check";
}