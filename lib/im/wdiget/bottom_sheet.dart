import 'package:flutter/material.dart';

class RCIWBottomSheet {
  static showSheet(List<Widget> items, BuildContext context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,  // 添加这个参数
        shape: const RoundedRectangleBorder( // 可选：美化底部弹窗边缘
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(16),
          ),
        ),
        builder: (BuildContext context) {
          // 获取底部安全区域的高度
          final bottomPadding = MediaQuery.of(context).padding.bottom;
          return Padding(
            padding: EdgeInsets.only(bottom: bottomPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: items,
            ),
          );
        });
  }
}
