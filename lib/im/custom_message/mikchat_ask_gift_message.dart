import 'package:rongcloud_im_wrapper_plugin/rongcloud_im_wrapper_plugin.dart';
import 'dart:convert' as json_lib show json;

// 自定义消息需要继承 RCIMIWUserCustomMessage
//
class MikChatAskGiftMessage extends RCIMIWUserCustomMessage {
  String? type;
  String? channelId;
  String? giftCode;
  String? userId;
  String? userCategory;
  String? headFileName;
  String? giveNum;
  String? nickName;
  String? userRole;

  String? giftUrl;
  String? giftName;
  String? giftPrice;
  String? giftId;

  // 1. 定义自己的构造方法,需要调用父类的 RCIMIWUserCustomMessage(RCIMIWConversationType type, String targetId)
  MikChatAskGiftMessage(
      RCIMIWConversationType type1,
      String targetId,
      this.type,
      this.channelId,
      this.giftCode,
      this.userId,
      this.userCategory,
      this.headFileName,
      this.giveNum,
      this.nickName,
      this.userRole,
      this.giftUrl,
      this.giftName,
      this.giftPrice,
      this.giftId)
      : super(type1, targetId);

  // 2. 需要继承父类的构造函数
  MikChatAskGiftMessage.fromJson(Map<String, dynamic> json)
      : super.fromJson(json);

  // 3. 需要实现父类的 decode/encode/messageObjectName
  @override
  void decode(String jsonStr) {
    Map map = json_lib.json.decode(jsonStr.toString());
    // 获取的 key 值要与原生传递的 key 值一样
    type = map['type'];
    channelId = map['channelId'];
    giftCode = map['giftCode'];
    userId = map['userId'];
    userCategory = map['userCategory'];
    headFileName = map['headFileName'];
    giveNum = map['giveNum'];
    nickName = map['nickName'];
    userRole = map['userRole'];
    giftUrl = map['giftUrl'];
    giftName = map['giftName'];
    giftPrice = map['giftPrice'];
    giftId = map['giftId'];
  }

  @override
  String encode() {
    Map map = {};
    // 传递的 key 值要与原生传递的 key 值一样
    map['type'] = type;
    map['channelId'] = channelId;
    map['giftCode'] = giftCode;
    map['userId'] = userId;
    map['userCategory'] = userCategory;
    map['headFileName'] = headFileName;
    map['giveNum'] = giveNum;
    map['nickName'] = nickName;
    map['userRole'] = userRole;
    map['giftUrl'] = giftUrl;
    map['giftName'] = giftName;
    map['giftPrice'] = giftPrice;
    map['giftId'] = giftId;
    return json_lib.json.encode(map);
  }

  @override
  String messageObjectName() {
    return "mikchat:askgift";
  }

  // 4. 需要实现父类的 toJson
  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    // 此处 'content' 不可修改
    json['content'] = encode();
    return json;
  }
}
