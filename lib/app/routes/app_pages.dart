import 'package:get/get.dart';

import '../../pages/aiaVideo/bindings/aia_video_binding.dart';
import '../../pages/aiaVideo/views/aia_video_view.dart';
import '../../pages/callHistory/bindings/call_history_binding.dart';
import '../../pages/callHistory/views/call_history_view.dart';
import '../../pages/chat/bindings/chat_binding.dart';
import '../../pages/chat/views/chat_view.dart';
import '../../pages/conversationList/bindings/conversation_list_binding.dart';
import '../../pages/conversationList/views/conversation_list_view.dart';
import '../../pages/home/<USER>/home_binding.dart';
import '../../pages/home/<USER>/home_view.dart';
import '../../pages/login/bindings/login_binding.dart';
import '../../pages/login/views/login_view.dart';
import '../../pages/mainPage/bindings/main_page_binding.dart';
import '../../pages/mainPage/views/main_page_view.dart';
import '../../pages/match/bindings/match_binding.dart';
import '../../pages/match/views/match_view.dart';
import '../../pages/mine/bindings/mine_binding.dart';
import '../../pages/mine/sub/wallet/bindings/wallet_binding.dart';
import '../../pages/mine/sub/wallet/views/wallet_view.dart';
import '../../pages/mine/views/mine_view.dart';
import '../../pages/profile/bindings/profile_binding.dart';
import '../../pages/profile/views/profile_view.dart';
import '../../pages/splash/bindings/splash_binding.dart';
import '../../pages/splash/views/splash_view.dart';
import '../../pages/videoChat/bindings/video_chat_binding.dart';
import '../../pages/videoChat/views/video_chat_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;
  static final routes = [
    GetPage(
      name: _Paths.CONVERSATION_LIST,
      page: () => const ConversationListView(),
      binding: ConversationListBinding(),
    ),
    GetPage(
      name: _Paths.MAIN_PAGE,
      page: () => const MainPageView(),
      binding: MainPageBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
      preventDuplicates: true,
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.CHAT,
      page: () => const ChatView(),
      binding: ChatBinding(),
      preventDuplicates: true,
    ),
    GetPage(
        name: _Paths.MATCH,
        page: () => const MatchView(),
        binding: MatchBinding(),
        preventDuplicates: true),
    GetPage(
      name: _Paths.MINE,
      page: () => const MineView(),
      binding: MineBinding(),
      preventDuplicates: true,
    ),
    GetPage(
      name: _Paths.CALL_HISTORY,
      page: () => const CallHistoryView(),
      binding: CallHistoryBinding(),
    ),
    GetPage(
      name: _Paths.WALLET,
      page: () => const WalletView(),
      binding: WalletBinding(),
    ),
    GetPage(
      name: _Paths.VIDEO_CHAT,
      page: () => const VideoChatView(),
      binding: VideoChatBinding(),
    ),
    GetPage(
      name: _Paths.AIA_VIDEO,
      page: () => const AiaVideoView(),
      binding: AiaVideoBinding(),
    ),
  ];
}
