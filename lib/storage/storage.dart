import 'package:shared_preferences/shared_preferences.dart';

class StorageKeys {
  static const String TOKEN_KEY = 'Blade-Auth';
  static const String USER_KEY = 'user_info';
  static const String CACHE_DIR_KEY = 'CACHE_DIR_KEY';
  static const String RONGYUN_TOKEN_KEY = 'rongyun_token';
  static const String DEVICE_ID = 'device_id';
  static const String systemConfig = 'systemConfig';
  static const String doNotDisturb = 'doNotDisturb';
}

class Storage {
  Storage._internal();

  static final Storage _instance = Storage._internal();

  static late SharedPreferences _prefs; // 使用late关键字，但确保在使用前初始化

  // 工厂构造器，确保_prefs在首次实例化时被初始化
  factory Storage() {
    _instance.init();
    return _instance;
  }

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> set(String key, dynamic value) async {
    if (value is String) {
      await _prefs.setString(key, value);
    } else if (value is int) {
      await _prefs.setInt(key, value);
    } else if (value is double) {
      await _prefs.setDouble(key, value);
    } else if (value is bool) {
      await _prefs.setBool(key, value);
    } else if (value is List<String>) {
      await _prefs.setStringList(key, value);
    }
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  bool getBool(String key) {
    return _prefs.getBool(key) ?? false;
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  void delete(String key) async {}
}
