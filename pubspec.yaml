name: Chamatch
description: "1 v 1 video chat"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+3

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.18.0


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  http: ^1.2.1
  dio: ^5.4.3+1
  flutter_easyloading: ^3.0.5
  package_info_plus: ^8.0.0
  permission_handler: ^11.3.1
  badges: ^3.1.2
  rongcloud_im_wrapper_plugin: 5.12.1+1
  easy_refresh: 3.4.0
  path_provider: ^2.1.5
  device_info_plus: ^11.1.1
  shared_preferences: ^2.2.3
  isar_flutter_libs: 3.1.0+1
  hive: 2.2.3
  hive_flutter: 1.1.0
  logger: ^2.5.0
  pull_to_refresh: 2.0.0
  get: ^4.6.6
  cached_network_image: 3.4.1
  flutter_sound: 9.6.0
  file_picker: 8.1.6
  image_picker: 1.1.2
  image_picker_web: 4.0.0
  record: 5.0.0
#  record: 3.0.0
  just_audio: 0.9.42
  agora_rtc_engine: ^6.5.0
  json_annotation: ^4.9.0
  json_serializable: ^6.9.0
  build_runner: ^2.4.13
  url_launcher: ^6.0.9
  google_sign_in: ^6.1.0
  firebase_auth: ^4.8.0 # 如果需要与 Firebase 集成
  video_player: 2.9.2
  flutter_inapp_purchase: ^5.6.1
  facebook_app_events: ^0.19.5
  android_play_install_referrer: ^0.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

flutter_launcher_icons:
  image_path: "assets/ic_logo.png"
  android:
    generate: false
    image_path_android: "assets/ic_logo.png"
    # min_sdk_android: 21 # android min sdk min:16, default 21
  ios:
    generate: false
    remove_alpha_ios: true
    image_path_ios: "assets/ic_logo.png"
  web:
    generate: false
    image_path: "assets/ic_logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: false
    image_path: "assets/ic_logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/ic_logo.png"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/
    - assets/countries/
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
